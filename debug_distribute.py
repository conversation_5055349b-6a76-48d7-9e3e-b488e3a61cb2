"""
调试分布排列功能的脚本
帮助分析为什么分布排列没有效果
"""

def test_data_format_recognition():
    """测试数据格式识别"""
    
    # 根据用户反馈，数据格式是"15分钟"和"60分钟"
    test_cases = [
        # 15分钟数据格式
        "000001 平安银行 15分钟 2024-07-30 09:30",
        "600000 浦发银行 15分钟 2024-07-30 10:00",
        "000002 万科A 15分钟 2024-07-29 14:30",
        "平安银行 15分钟 2024-07-30 09:30",
        "浦发银行 15分钟 2024-07-30 10:00",

        # 60分钟数据格式
        "000001 平安银行 60分钟 2024-07-30 15:00",
        "600000 浦发银行 60分钟 2024-07-30 15:00",
        "000002 万科A 60分钟 2024-07-29 15:00",
        "平安银行 60分钟 2024-07-30 15:00",
        "浦发银行 60分钟 2024-07-30 15:00",

        # 其他可能格式
        "000001 平安银行 日线 2024-07-30",
        "000001 平安银行 周线 2024-07-30",
        "其他数据",
    ]
    
    def extract_stock_and_date(text):
        """从文本中提取股票名称和日期信息"""
        try:
            print(f"提取股票和日期信息: {text}")

            # 处理15分钟数据格式
            if '15分钟' in text:
                # 尝试多种可能的分隔符
                separators = [' 15分钟_', ' 15分钟 ', '_15分钟_', '_15分钟 ']
                for separator in separators:
                    if separator in text:
                        parts = text.split(separator)
                        if len(parts) == 2:
                            stock_name = parts[0].strip()
                            datetime_str = parts[1].strip()
                            # 提取日期部分
                            date_part = datetime_str.split(' ')[0] if ' ' in datetime_str else datetime_str.split('_')[0]
                            print(f"  15分钟数据({separator}) - 股票: {stock_name}, 日期: {date_part}")
                            return stock_name, date_part

                # 如果没有找到分隔符，尝试简单的空格分割
                if ' ' in text:
                    parts = text.split(' ')
                    if len(parts) >= 3:  # 至少包含股票名、15分钟、日期
                        stock_name = parts[0].strip()
                        # 找到日期部分（通常包含-或数字）
                        for part in parts[2:]:
                            if '-' in part or part.replace(':', '').replace('-', '').isdigit():
                                date_part = part.split(' ')[0] if ' ' in part else part
                                print(f"  15分钟数据(空格分割) - 股票: {stock_name}, 日期: {date_part}")
                                return stock_name, date_part

            # 处理60分钟数据格式
            elif '60分钟' in text:
                # 尝试多种可能的分隔符
                separators = [' 60分钟_', ' 60分钟 ', '_60分钟_', '_60分钟 ']
                for separator in separators:
                    if separator in text:
                        parts = text.split(separator)
                        if len(parts) == 2:
                            stock_name = parts[0].strip()
                            datetime_str = parts[1].strip()
                            # 提取日期部分
                            date_part = datetime_str.split(' ')[0] if ' ' in datetime_str else datetime_str.split('_')[0]
                            print(f"  60分钟数据({separator}) - 股票: {stock_name}, 日期: {date_part}")
                            return stock_name, date_part

                # 如果没有找到分隔符，尝试简单的空格分割
                if ' ' in text:
                    parts = text.split(' ')
                    if len(parts) >= 3:  # 至少包含股票名、60分钟、日期
                        stock_name = parts[0].strip()
                        # 找到日期部分（通常包含-或数字）
                        for part in parts[2:]:
                            if '-' in part or part.replace(':', '').replace('-', '').isdigit():
                                date_part = part.split(' ')[0] if ' ' in part else part
                                print(f"  60分钟数据(空格分割) - 股票: {stock_name}, 日期: {date_part}")
                                return stock_name, date_part

            print(f"  无法识别格式，返回原文本: {text}")
            return text, ""
        except Exception as e:
            print(f"  提取失败: {e}")
            return text, ""
    
    print("=== 测试数据格式识别 ===")
    for case in test_cases:
        stock_name, date = extract_stock_and_date(case)
        print(f"结果: 股票='{stock_name}', 日期='{date}'")
        print("-" * 50)

def test_classification():
    """测试数据分类逻辑"""
    
    test_items = [
        "000001 平安银行 15分钟_2024-07-30 09:30",
        "000001 平安银行_60m_2024-07-30 1500", 
        "600000 浦发银行 15分钟_2024-07-30 10:00",
        "600000 浦发银行_60m_2024-07-30 1500",
        "其他数据",
        "000002 万科A 日线_2024-07-30"
    ]
    
    minute_15_items = []
    minute_60_items = []
    other_items = []
    
    print("\n=== 测试数据分类 ===")
    for text in test_items:
        if '15分钟' in text:
            minute_15_items.append(text)
            print(f"识别为15分钟数据: {text}")
        elif '60m' in text or '60分钟' in text or '1h' in text or '1H' in text:
            minute_60_items.append(text)
            print(f"识别为60分钟数据: {text}")
        else:
            other_items.append(text)
            print(f"识别为其他数据: {text}")
    
    print(f"\n分类结果:")
    print(f"15分钟数据: {len(minute_15_items)} 个")
    print(f"60分钟数据: {len(minute_60_items)} 个")
    print(f"其他数据: {len(other_items)} 个")

def provide_debugging_tips():
    """提供调试建议"""
    print("\n=== 调试建议 ===")
    print("1. 运行股票技术信息浏览器程序")
    print("2. 加载HDF5文件")
    print("3. 在股票列表中选择一些项目")
    print("4. 右键点击选择'分布排列'")
    print("5. 查看控制台输出的调试信息")
    print("\n调试信息会显示:")
    print("- 所有项目的文本内容")
    print("- 数据分类结果")
    print("- 股票名称和日期提取结果")
    print("- 重建列表的新顺序")
    print("\n如果没有看到调试信息，可能的原因:")
    print("- 没有选中任何项目")
    print("- 数据格式与预期不符")
    print("- 程序运行出错")

if __name__ == "__main__":
    test_data_format_recognition()
    test_classification()
    provide_debugging_tips()
