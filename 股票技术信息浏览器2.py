"""
股票看盘软件 - 完全修复版本
基于原始工作的双缓冲机制，删除所有覆盖层代码
保持原始版本的完整样式和窗体结构
"""

import sys
import os
import warnings
import numpy as np
import pandas as pd

# 确保使用PyQt6
os.environ['QT_API'] = 'pyqt6'

# 设置matplotlib后端
import matplotlib
matplotlib.use('QtAgg')
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D
import mplfinance as mpf

# 设置中文字体 - 支持多种系统
def setup_chinese_font():
    """设置中文字体"""
    import platform
    system = platform.system()

    if system == "Windows":
        fonts = ['Microsoft YaHei', 'SimHei', 'SimSun']
    elif system == "Darwin":  # macOS
        fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti']
    else:  # Linux
        fonts = ['WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'DejaVu Sans']

    # 添加默认字体作为备选
    fonts.extend(['DejaVu Sans', 'Arial', 'sans-serif'])

    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False

    return fonts[0]  # 返回首选字体

# 设置字体
preferred_font = setup_chinese_font()

# 导入matplotlib的PyQt6后端
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qtagg import NavigationToolbar2QT as NavigationToolbar
from matplotlib.figure import Figure

class CustomNavigationToolbar(NavigationToolbar):
    """自定义工具栏 - 点击按钮时自动让canvas获得焦点"""

    def __init__(self, canvas, parent):
        super().__init__(canvas, parent)
        self.canvas = canvas

    def zoom(self, *args):
        """重写zoom方法，点击放大镜按钮时让canvas获得焦点"""
        result = super().zoom(*args)
        # 让canvas获得焦点，这样ESC键就能生效
        self._ensure_canvas_focus()
        # 重置工具栏缩放标志
        if hasattr(self.canvas, 'toolbar_has_zoomed'):
            self.canvas.toolbar_has_zoomed = False
        return result

    def pan(self, *args):
        """重写pan方法，点击拖拽按钮时让canvas获得焦点"""
        result = super().pan(*args)
        # 让canvas获得焦点，这样ESC键就能生效
        self._ensure_canvas_focus()
        # 重置工具栏缩放标志
        if hasattr(self.canvas, 'toolbar_has_zoomed'):
            self.canvas.toolbar_has_zoomed = False
        return result

    def release_zoom(self, event):
        """重写release_zoom方法，在缩放完成后设置标志"""
        result = super().release_zoom(event)
        # 缩放操作完成，设置标志
        if hasattr(self.canvas, 'toolbar_has_zoomed'):
            self.canvas.toolbar_has_zoomed = True
        return result

    def release_pan(self, event):
        """重写release_pan方法，在拖拽完成后设置标志"""
        result = super().release_pan(event)
        # 拖拽操作完成，设置标志
        if hasattr(self.canvas, 'toolbar_has_zoomed'):
            self.canvas.toolbar_has_zoomed = True
        return result

    def home(self, *args):
        """重写home方法，点击home按钮时让canvas获得焦点"""
        result = super().home(*args)
        self._ensure_canvas_focus()
        return result

    def back(self, *args):
        """重写back方法，点击后退按钮时让canvas获得焦点"""
        result = super().back(*args)
        self._ensure_canvas_focus()
        return result

    def forward(self, *args):
        """重写forward方法，点击前进按钮时让canvas获得焦点"""
        result = super().forward(*args)
        self._ensure_canvas_focus()
        return result

    def _ensure_canvas_focus(self):
        """确保canvas获得焦点"""
        try:
            if hasattr(self.canvas, 'setFocus'):
                self.canvas.setFocus()
                # 额外确保焦点策略正确
                if hasattr(self.canvas, 'setFocusPolicy'):
                    from PyQt6.QtCore import Qt
                    self.canvas.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        except Exception as e:
            pass

# 导入PyQt6
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtGui import QKeyEvent

from tables import NaturalNameWarning
warnings.filterwarnings("ignore", category=NaturalNameWarning, module="tables.path")

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QFileDialog, QLineEdit, QLabel, QMessageBox,
    QComboBox, QTextEdit, QFrame, QSplitter, QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt, QTimer, QTimer
from PyQt6.QtGui import QFont, QAction, QPixmap, QPainter, QPen, QColor, QDragMoveEvent

class AutoScrollListWidget(QListWidget):
    """支持拖拽时自动滚动的QListWidget"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.scroll_timer = QTimer()
        self.scroll_timer.timeout.connect(self.auto_scroll)
        self.scroll_direction = 0  # 1向下滚动，-1向上滚动，0不滚动
        self.scroll_speed = 1  # 滚动速度

    def dragMoveEvent(self, event: QDragMoveEvent):
        """拖拽移动事件，处理自动滚动"""
        super().dragMoveEvent(event)

        # 获取视口区域
        viewport_rect = self.viewport().rect()
        pos = event.position().toPoint()

        # 定义滚动触发区域的高度（像素）
        scroll_zone_height = 20

        # 检查是否需要向上滚动
        if pos.y() < scroll_zone_height:
            if not self.scroll_timer.isActive():
                self.scroll_direction = -1
                self.scroll_timer.start(50)  # 每50ms滚动一次
        # 检查是否需要向下滚动
        elif pos.y() > viewport_rect.height() - scroll_zone_height:
            if not self.scroll_timer.isActive():
                self.scroll_direction = 1
                self.scroll_timer.start(50)  # 每50ms滚动一次
        else:
            # 停止自动滚动
            if self.scroll_timer.isActive():
                self.scroll_timer.stop()
                self.scroll_direction = 0

    def dragLeaveEvent(self, event):
        """拖拽离开事件，停止自动滚动"""
        super().dragLeaveEvent(event)
        if self.scroll_timer.isActive():
            self.scroll_timer.stop()
            self.scroll_direction = 0

    def dropEvent(self, event):
        """拖拽放下事件，停止自动滚动"""
        if self.scroll_timer.isActive():
            self.scroll_timer.stop()
            self.scroll_direction = 0
        super().dropEvent(event)

    def auto_scroll(self):
        """自动滚动函数"""
        if self.scroll_direction == 0:
            return

        # 获取当前滚动条位置
        scrollbar = self.verticalScrollBar()
        current_value = scrollbar.value()

        # 计算新的滚动位置
        if self.scroll_direction == -1:  # 向上滚动
            new_value = max(0, current_value - self.scroll_speed)
        else:  # 向下滚动
            new_value = min(scrollbar.maximum(), current_value + self.scroll_speed)

        # 设置新的滚动位置
        scrollbar.setValue(new_value)

        # 如果已经到达边界，停止滚动
        if new_value == current_value:
            self.scroll_timer.stop()
            self.scroll_direction = 0

# 均线配置
ma_config = {
    'ma5': {'color': '#D3D3D3', 'name': 'MA5'},
    'ma10': {'color': '#ffe4ae', 'name': 'MA10'},
    'ma20': {'color': '#e123e7', 'name': 'MA20'},
    'ma30': {'color': '#2cb02c', 'name': 'MA30'},
    'ma60': {'color': '#747474', 'name': 'MA60'},
    'ma120': {'color': '#8ba2c4', 'name': 'MA120'},
    'ma250': {'color': '#92d2ff', 'name': 'MA250'}
}

class ManualKLineCanvas(FigureCanvas):
    """手动绘制K线的Canvas"""

    def __init__(self, parent=None):
        self.figure = Figure(figsize=(20, 10))  # 从15调整到20，增加宽度
        super().__init__(self.figure)
        self.setParent(parent)

        self.df = None
        self.axes = []
        self.crosshair_lines = []
        self.crosshair_enabled = False  # 十字光标状态
        self.current_crosshair_index = 0  # 当前十字光标位置索引

        # 缩放相关变量
        self.zoom_mode = False  # 缩放模式状态
        self.original_xlim = None  # 原始X轴范围
        self.original_ylim = None  # 原始Y轴范围
        self.zoom_factor = 1.2  # 缩放因子
        self.grid_lines = []  # 保存网格线引用
        self.is_zoomed = False  # 标记是否已经缩放（包括鼠标框选）

        # 工具栏状态监控
        self.last_toolbar_mode = None
        self.toolbar_check_timer = QTimer()
        self.toolbar_check_timer.timeout.connect(self.check_toolbar_status)

        # 工具栏模式下的缩放状态跟踪
        self.toolbar_has_zoomed = False  # 在工具栏模式下是否进行过缩放

        # 键盘事件防重复处理
        self.esc_processing = False
        self.key_processing = False

        # 连接鼠标和键盘事件
        self.mpl_connect('motion_notify_event', self.on_mouse_move)
        self.mpl_connect('scroll_event', self.on_scroll)
        self.mpl_connect('button_press_event', self.on_mouse_click)
        self.mpl_connect('key_press_event', self.on_key_press)
        self.mpl_connect('resize_event', self.on_resize)  # 添加窗口大小变化事件

        # 确保canvas可以获得焦点（Qt版本）
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        self.setFocus()

    def keyPressEvent(self, event):
        """Qt键盘事件处理 - 作为matplotlib事件的备用方案"""
        print(f"Canvas keyPressEvent: {event.key()}, text: {event.text()}")

        # 将Qt键盘事件转换为类似matplotlib的格式
        key_map = {
            Qt.Key.Key_C: 'c',
            Qt.Key.Key_Q: 'q',
            Qt.Key.Key_Escape: 'escape',
            Qt.Key.Key_Left: 'left',
            Qt.Key.Key_Right: 'right',
            Qt.Key.Key_Up: 'up',
            Qt.Key.Key_Down: 'down'
        }

        if event.key() in key_map:
            print(f"Key mapped: {key_map[event.key()]}")
            # 创建一个模拟的matplotlib事件对象
            class MockEvent:
                def __init__(self, key):
                    self.key = key

            mock_event = MockEvent(key_map[event.key()])
            self.on_key_press(mock_event)
        else:
            print(f"Key not mapped: {event.key()}")

        super().keyPressEvent(event)

    def plot_stock_data_double_buffer(self, df, title="股票图表"):
        """真正的双缓冲绘制 - 保持十字光标功能"""

        self.df = df.copy()

        # 清除旧的十字光标

        self.clear_crosshair()


        
        try:
            from PyQt6.QtCore import QTimer
            from PyQt6.QtWidgets import QApplication
            
            # 禁用更新避免闪烁
            if hasattr(self, 'setUpdatesEnabled'):
                self.setUpdatesEnabled(False)
            
            # 清除并重绘
            self.figure.clear()
            self.plot_manual_fallback(df, title)
            
            # 启用更新并刷新
            if hasattr(self, 'setUpdatesEnabled'):
                self.setUpdatesEnabled(True)
            
            self.draw()
            QApplication.processEvents()
            


            # 连接轴范围变化事件，处理工具栏缩放
            self.connect_axis_events()

        except Exception as e:
            pass

            # 确保canvas更新被重新启用
            if hasattr(self, 'setUpdatesEnabled'):
                self.setUpdatesEnabled(True)
            pass



    def plot_manual_fallback(self, df, title):
        """手动绘制备用方案 - 完全按照原始版本"""


        # 清除旧的十字光标引用（重要！）

        self.crosshair_lines.clear()


        # 创建子图 - 使用原始版本的比例设置
        if 'volume' in df.columns:
            pass

            from matplotlib.gridspec import GridSpec
            gs = GridSpec(2, 1, height_ratios=[4, 1], hspace=0.05)
            ax1 = self.figure.add_subplot(gs[0])
            ax2 = self.figure.add_subplot(gs[1])
            self.axes = [ax1, ax2]

        else:
            pass

            ax1 = self.figure.add_subplot(1, 1, 1)
            self.axes = [ax1]



        # 手动绘制K线
        self.draw_candlesticks_manual(ax1, df)



        # 绘制均线
        self.draw_moving_averages(ax1, df)



        # 设置Y轴范围 - 确保覆盖K线和均线的最高最低价
        self.set_y_axis_range(ax1, df)



        # 绘制信号
        if 'signal' in df.columns:
            pass

            self.draw_signals(ax1, df)

        else:
            pass


        # 绘制成交量
        if 'volume' in df.columns and len(self.axes) > 1:
            self.draw_volume(self.axes[1], df)

        # 设置样式
        self.apply_styles(title)
        self.draw()

    def draw_candlesticks_manual(self, ax, df):
        """手动绘制K线 - 使用最简单可靠的方法"""

        # 由于figure已经在上层清除过，这里不需要额外清除
        # 直接开始绘制，避免黑框闪烁

        red_count = 0
        green_count = 0

        # 使用最简单的绘制方法 - 全部用线条绘制，避免Rectangle的渲染问题
        wick_width = 0.8     # 影线宽度
        body_width = 2.0     # 实体线宽（调整为更合适的宽度，避免重叠）
        alpha_value = 1.0    # 统一透明度



        # 禁用交互模式，避免中间状态显示
        plt.ioff()

        for i, (idx, row) in enumerate(df.iterrows()):
            pass

            try:
                open_price = float(row['open'])
                high_price = float(row['high'])
                low_price = float(row['low'])
                close_price = float(row['close'])
            except (ValueError, TypeError):
                continue

            # 确定颜色
            if close_price > open_price:
                color = '#FF0000'  # 红色阳线
                red_count += 1
                candle_type = "阳线"
            elif close_price < open_price:
                color = '#009F00'  # 绿色阴线
                green_count += 1
                candle_type = "阴线"
            else:
                color = '#808080'  # 灰色十字星
                candle_type = "十字星"

            # 计算实体范围
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)
            body_top = max(open_price, close_price)

            # 计算K线中心位置
            center_x = float(i)  # K线的精确中心X坐标

            # 绘制影线 - 使用vlines确保完全垂直和居中
            if high_price != low_price:  # 只有当有价格差异时才绘制影线
                ax.vlines(center_x, low_price, high_price,
                         colors=color, linewidth=wick_width, alpha=alpha_value)

            # 绘制实体 - 使用粗线代替Rectangle
            if body_height > 1e-8:  # 有实体的K线
                # 使用粗线绘制实体，完全避免Rectangle的渲染问题
                ax.vlines(center_x, body_bottom, body_top,
                         colors=color, linewidth=body_width, alpha=alpha_value)
            else:  # 十字星（开盘价等于收盘价）
                # 十字星横线
                ax.hlines(close_price, center_x - 0.3, center_x + 0.3,
                         colors=color, linewidth=wick_width, alpha=alpha_value)

        # 设置X轴范围，确保K线不贴边
        ax.set_xlim(-0.5, len(df) - 0.5)

        # 重新启用交互模式
        plt.ion()



        # 强制设置图形渲染参数
        ax.figure.canvas.draw()
        ax.figure.canvas.flush_events()  # 强制刷新事件

        # 设置精确渲染模式
        ax.set_rasterization_zorder(0)  # 禁用栅格化
        ax.set_aspect('auto')  # 确保不会因为纵横比导致变形

    def set_y_axis_range(self, ax, df):
        """设置Y轴范围和刻度 - 最高最低刻度对应实际最高最低价"""
        try:
            # 收集所有价格数据：K线的高低价 + 所有均线
            price_data = []

            # K线的高低价
            if 'high' in df.columns:
                price_data.extend(df['high'].dropna().tolist())
            if 'low' in df.columns:
                price_data.extend(df['low'].dropna().tolist())

            # 所有均线数据（支持大小写）
            ma_columns = [col for col in df.columns if col.lower().startswith('ma') and col.lower()[2:].isdigit()]
            for ma_col in ma_columns:
                ma_data = df[ma_col].dropna()
                if not ma_data.empty:
                    price_data.extend(ma_data.tolist())

            if not price_data:
                return

            # 计算实际的最高最低价
            actual_min_price = min(price_data)
            actual_max_price = max(price_data)
            price_range = actual_max_price - actual_min_price

            # 设置Y轴范围 - 在底部留出小空间避免最低刻度线与边框重合
            price_range = actual_max_price - actual_min_price
            bottom_margin = price_range * 0.01  # 底部留出1%的空间
            ax.set_ylim(actual_min_price - bottom_margin, actual_max_price)

            # 禁用自动边距调整，确保严格按照设置的范围显示
            ax.margins(0)

            # 强制设置Y轴范围，防止matplotlib自动调整
            ax.set_autoscaley_on(False)

            # 设置Y轴刻度 - 确保最高最低刻度显示实际价格
            import numpy as np

            # 生成精确的5个刻度，包含最高最低价
            if price_range > 0:
                tick_count = 5  # 精确5个刻度
                tick_interval = price_range / (tick_count - 1)

                # 生成刻度位置
                ticks = []
                for i in range(tick_count):
                    tick_value = actual_min_price + i * tick_interval
                    ticks.append(tick_value)

                # 确保最后一个刻度是精确的最高价
                ticks[-1] = actual_max_price

                # 设置刻度
                ax.set_yticks(ticks)

                # 格式化刻度标签
                tick_labels = [f'{tick:.2f}' for tick in ticks]
                ax.set_yticklabels(tick_labels)

                # 清除旧的网格线并添加新的横轴线
                self.clear_grid_lines()
                for i, tick in enumerate(ticks):
                    # 最低价刻度线使用稍微突出的样式，但位置保持准确
                    if i == 0:  # 最低价刻度线
                        hline = ax.axhline(y=tick, color='gray', linestyle='-', alpha=0.6, linewidth=0.8, zorder=2)
                    else:
                        hline = ax.axhline(y=tick, color='gray', linestyle='-', alpha=0.4, linewidth=0.5, zorder=1)
                    self.grid_lines.append(hline)

            else:
                # 价格范围为0的特殊情况
                ax.set_yticks([actual_min_price])
                ax.set_yticklabels([f'{actual_min_price:.2f}'])
                self.clear_grid_lines()
                hline = ax.axhline(y=actual_min_price, color='gray', linestyle='-', alpha=0.4, linewidth=0.5, zorder=1)
                self.grid_lines.append(hline)

        except Exception as e:
            pass

    def draw_moving_averages(self, ax, df):
        """绘制均线 - 不显示图例"""
        try:
            x_pos = range(len(df))

            # 支持大小写的均线列名
            for ma_col, config in ma_config.items():
                # 先检查小写版本
                if ma_col in df.columns and not df[ma_col].isna().all():
                    ax.plot(x_pos, df[ma_col],
                           color=config['color'],
                           linewidth=1.5,
                           alpha=0.8)
                # 再检查大写版本
                elif ma_col.upper() in df.columns and not df[ma_col.upper()].isna().all():
                    ax.plot(x_pos, df[ma_col.upper()],
                           color=config['color'],
                           linewidth=1.5,
                           alpha=0.8)

            # 不添加图例，保持图表简洁

        except Exception as e:
            pass

    def draw_signals(self, ax, df):
        """绘制信号标记"""
        try:
            signal_points = df[df['signal'] == True]
            if not signal_points.empty:
                x_pos = [df.index.get_loc(idx) for idx in signal_points.index]
                y_pos = signal_points['low'] * 0.995
                
                ax.scatter(x_pos, y_pos, marker='^', s=80, 
                          color='#4F4FFB', alpha=0.7, zorder=5)
            
            
        except Exception as e:
            pass

    def draw_volume(self, ax, df):
        """绘制成交量 - 标准柱状图样式"""
        if 'volume' not in df.columns:
            return

        volume_data = df['volume'].dropna()
        if volume_data.empty:
            return

        # 成交量颜色与K线保持一致
        colors = []
        for c, o in zip(df['close'], df['open']):
            pass
            if c > o:
                colors.append('#FF0000')  # 红色 - 上升
            elif c < o:
                colors.append('#009F00')  # 绿色 - 下跌
            else:
                colors.append('#808080')  # 灰色 - 平盘

        # 绘制成交量柱状图
        x_pos = range(len(df))
        ax.bar(x_pos, df['volume'], color=colors, alpha=0.8, width=0.8)

        # 设置X轴范围与主图一致
        ax.set_xlim(-0.5, len(df) - 0.5)

        # 锁定成交量图的Y轴范围，防止被十字光标影响
        volume_min = df['volume'].min()
        volume_max = df['volume'].max()
        volume_range = volume_max - volume_min
        margin = volume_range * 0.05 if volume_range > 0 else volume_max * 0.1
        ax.set_ylim(volume_min - margin, volume_max + margin)


    def apply_styles(self, title):
        """应用样式 - 参考图片样式"""
        if not self.axes:
            return

        # 主图样式 - 按照visualization.py
        ax1 = self.axes[0]
        ax1.set_title(title, fontsize=14, pad=20)
        # 完全关闭网格线，按照visualization.py
        ax1.xaxis.grid(False)
        ax1.yaxis.grid(False)
        # 设置合适的X轴范围，让K线间距更宽松
        ax1.set_xlim(-0.5, len(self.df)-0.5)

        # 移除所有边框
        for spine in ax1.spines.values():
            spine.set_visible(False)

        # 价格显示到左侧
        ax1.yaxis.tick_left()
        ax1.yaxis.set_label_position('left')
        ax1.tick_params(axis='y', labelsize=10, left=True, right=False)
        ax1.yaxis.set_major_formatter(plt.FormatStrFormatter('%.2f'))

        # 主图不显示X轴刻度
        ax1.tick_params(axis='x', which='both', bottom=False, labelbottom=False)

        # 如果有成交量图 - 按照visualization.py
        if len(self.axes) > 1:
            ax2 = self.axes[1]
            # 完全关闭网格线，按照visualization.py
            ax2.xaxis.grid(False)
            ax2.yaxis.grid(False)
            # 成交量图使用相同的X轴范围
            ax2.set_xlim(-0.5, len(self.df)-0.5)

            # 移除所有边框
            for spine in ax2.spines.values():
                spine.set_visible(False)

            # 不添加价格和成交量之间的分界线

            # 成交量图只在下方显示时间轴
            self.setup_time_axis(ax2)

            # 隐藏成交量Y轴刻度
            ax2.yaxis.set_visible(False)

    def setup_time_axis(self, ax):
        """设置时间轴显示 - 固定显示5个时间刻度，24-Nov-07格式"""
        if self.df is None or len(self.df) == 0:
            return

        # 获取数据长度
        data_len = len(self.df)

        # 固定显示5个时间刻度
        if data_len <= 5:
            # 数据少于5个，显示所有
            tick_positions = list(range(data_len))
        else:
            # 数据多于5个，均匀分布5个刻度
            tick_positions = []
            # 第一个刻度：起始位置
            tick_positions.append(0)
            # 中间3个刻度：均匀分布
            for i in range(1, 4):
                pos = int(data_len * i / 4)
                tick_positions.append(pos)
            # 最后一个刻度：结束位置
            tick_positions.append(data_len - 1)

        # 生成时间标签 - 24-Nov-07格式
        tick_labels = []
        for pos in tick_positions:
            pass
            if pos < len(self.df):
                timestamp = self.df.index[pos]
                if hasattr(timestamp, 'strftime'):
                    # 格式化为 24-Nov-07 样式
                    formatted_date = timestamp.strftime('%d-%b-%y')
                    tick_labels.append(formatted_date)
                else:
                    tick_labels.append(str(timestamp))

        # 设置时间轴
        ax.set_xticks(tick_positions)
        ax.set_xticklabels(tick_labels, rotation=0, fontsize=9)
        ax.tick_params(axis='x', which='both', bottom=True, labelbottom=True)

    # 十字光标相关方法
    def toggle_crosshair(self):
        """切换十字光标显示"""
        self.crosshair_enabled = not self.crosshair_enabled
        if not self.crosshair_enabled:
            self.clear_crosshair()

    def clear_crosshair(self):
        """安全清除十字光标 - 只清除标记的十字光标线"""
        # 只清除记录在crosshair_lines中的线条
        for line in self.crosshair_lines:
            pass
            try:
                line.remove()
            except Exception as e:
                pass

        # 清空记录列表
        self.crosshair_lines.clear()

        # 强制刷新显示，确保十字光标完全消失
        self.draw_idle()

        # 额外的保险措施：强制重绘整个canvas
        try:
            self.draw()
            from PyQt6.QtWidgets import QApplication
            QApplication.processEvents()
        except:
            pass

    def update_crosshair(self, x, y, current_ax):
        """更新十字光标 - 完全按照原始版本"""
        # 保存最后的十字光标位置
        self.last_crosshair_pos = (x, y)

        # 清除旧的十字光标
        for line in self.crosshair_lines:
            pass
            try:
                line.remove()
            except:
                pass
        self.crosshair_lines.clear()

        # 绘制新的十字光标 - 使用最明显的样式
        if x is not None and self.axes:
            pass

            # 在所有子图显示垂直线 - 只使用axvline，避免影响Y轴范围
            for i, ax in enumerate(self.axes):
                pass
                try:
                    # 保存原始Y轴范围
                    original_ylim = ax.get_ylim()

                    # 使用深灰色虚线，优雅且可见
                    vline = ax.axvline(x, color='gray', linestyle='--', alpha=0.8, linewidth=1, zorder=1000)
                    self.crosshair_lines.append(vline)

                    # 强制恢复原始Y轴范围，防止被十字光标影响
                    ax.set_ylim(original_ylim)

                except Exception as e:
                    pass

            # 在当前轴显示水平线
            if y is not None and current_ax in self.axes:
                pass
                try:
                    # 保存原始轴范围
                    original_xlim = current_ax.get_xlim()
                    original_ylim = current_ax.get_ylim()

                    # 使用深灰色虚线，优雅且可见
                    hline = current_ax.axhline(y, color='gray', linestyle='--', alpha=0.8, linewidth=1, zorder=1000)
                    self.crosshair_lines.append(hline)

                    # 强制恢复原始轴范围，防止被十字光标影响
                    current_ax.set_xlim(original_xlim)
                    current_ax.set_ylim(original_ylim)

                except Exception as e:
                    pass


        self.draw_idle()

    def move_crosshair(self, direction):
        """移动十字光标位置"""
        if not self.crosshair_enabled or self.df is None:
            return

        # 更新当前索引
        self.current_crosshair_index += direction

        # 确保索引在有效范围内
        if self.current_crosshair_index < 0:
            self.current_crosshair_index = 0
        elif self.current_crosshair_index >= len(self.df):
            self.current_crosshair_index = len(self.df) - 1


        # 获取当前位置的数据
        row_data = self.df.iloc[self.current_crosshair_index]
        timestamp = self.df.index[self.current_crosshair_index]

        # 计算十字光标位置
        x_pos = float(self.current_crosshair_index)
        y_pos = float(row_data['close'])  # 使用收盘价作为Y坐标

        # 更新信息显示
        parent = self.parent()
        while parent and not hasattr(parent, 'update_info_display'):
            parent = parent.parent()

        if parent and hasattr(parent, 'update_info_display'):
            parent.update_info_display(timestamp, row_data)

        # 更新十字光标显示
        current_ax = self.axes[0] if self.axes else None
        self.update_crosshair(x_pos, y_pos, current_ax)

    # 缩放相关方法
    def save_original_limits(self):
        """保存原始的轴范围 - 基于数据的真实范围"""
        if self.df is not None and len(self.df) > 0:
            # 保存基于数据的真实范围，而不是当前显示范围
            self.original_xlim = (-0.5, len(self.df) - 0.5)
            actual_min_price = self.df['low'].min()
            actual_max_price = self.df['high'].max()
            self.original_ylim = (actual_min_price, actual_max_price)
        elif self.axes and len(self.axes) > 0:
            # 备用方案：如果没有数据，使用当前轴范围
            ax = self.axes[0]
            self.original_xlim = ax.get_xlim()
            self.original_ylim = ax.get_ylim()

    def zoom_in(self):
        """放大图表"""
        if not self.axes or len(self.axes) == 0:
            return

        ax = self.axes[0]  # 主图轴

        # 如果是第一次缩放，保存原始范围
        if not self.zoom_mode and not self.is_zoomed:
            self.save_original_limits()
            self.zoom_mode = True
            self.is_zoomed = True

        # 获取当前范围
        xlim = ax.get_xlim()
        ylim = ax.get_ylim()

        # 计算新的范围（缩小范围实现放大效果）
        x_center = (xlim[0] + xlim[1]) / 2
        y_center = (ylim[0] + ylim[1]) / 2
        x_range = (xlim[1] - xlim[0]) / self.zoom_factor
        y_range = (ylim[1] - ylim[0]) / self.zoom_factor

        new_xlim = (x_center - x_range/2, x_center + x_range/2)

        # 根据新的X轴范围计算实际的数据Y轴范围
        actual_min_price = None
        actual_max_price = None

        if self.df is not None and len(self.df) > 0:
            start_idx = max(0, int(new_xlim[0] + 0.5))
            end_idx = min(len(self.df) - 1, int(new_xlim[1] + 0.5))

            if start_idx <= end_idx:
                visible_data = self.df.iloc[start_idx:end_idx+1]

                # 计算所有相关数据的最高最低价
                all_prices = [visible_data['low'].min(), visible_data['high'].max()]

                # 考虑均线数据（支持大小写）
                ma_columns = [col for col in visible_data.columns if col.lower().startswith('ma')]
                for ma_col in ma_columns:
                    pass
                    if ma_col in visible_data.columns:
                        ma_data = visible_data[ma_col].dropna()
                        if len(ma_data) > 0:
                            all_prices.extend([ma_data.min(), ma_data.max()])

                actual_min_price = min(all_prices)
                actual_max_price = max(all_prices)

                # 为Y轴底部添加小边距
                price_range = actual_max_price - actual_min_price
                bottom_margin = price_range * 0.01
                new_ylim = (actual_min_price - bottom_margin, actual_max_price)

                if ma_columns:
                    pass
                    for ma_col in ma_columns:
                        pass
                        if ma_col in visible_data.columns:
                            ma_data = visible_data[ma_col].dropna()
                            if len(ma_data) > 0:
                                pass
            else:
                # 备用方案
                new_ylim = (y_center - y_range/2, y_center + y_range/2)
                y_range_calc = new_ylim[1] - new_ylim[0]
                bottom_margin = y_range_calc * 0.01
                new_ylim = (new_ylim[0] - bottom_margin, new_ylim[1])
                actual_min_price = new_ylim[0] + bottom_margin
                actual_max_price = new_ylim[1]
        else:
            # 备用方案
            new_ylim = (y_center - y_range/2, y_center + y_range/2)
            y_range_calc = new_ylim[1] - new_ylim[0]
            bottom_margin = y_range_calc * 0.01
            new_ylim = (new_ylim[0] - bottom_margin, new_ylim[1])
            actual_min_price = new_ylim[0] + bottom_margin
            actual_max_price = new_ylim[1]

        ax.set_xlim(new_xlim)
        ax.set_ylim(new_ylim)

        # 重新设置Y轴刻度（传入实际数据范围，不包含边距）
        if actual_min_price is not None and actual_max_price is not None:
            actual_data_ylim = (actual_min_price, actual_max_price)
            self.update_y_axis_ticks(ax, actual_data_ylim)
        else:
            # 如果没有计算出实际数据范围，重新计算
            self.recalculate_and_update_ticks(ax, new_xlim)

        # 同步成交量图的X轴
        if len(self.axes) > 1:
            self.axes[1].set_xlim(new_xlim)

        self.draw()

    def zoom_out(self):
        """缩小图表"""
        if not self.axes or len(self.axes) == 0:
            return

        ax = self.axes[0]  # 主图轴

        # 如果是第一次缩放，保存原始范围
        if not self.zoom_mode and not self.is_zoomed:
            self.save_original_limits()
            self.zoom_mode = True
            self.is_zoomed = True

        # 获取当前范围
        xlim = ax.get_xlim()
        ylim = ax.get_ylim()

        # 计算新的范围（扩大范围实现缩小效果）
        x_center = (xlim[0] + xlim[1]) / 2
        y_center = (ylim[0] + ylim[1]) / 2
        x_range = (xlim[1] - xlim[0]) * self.zoom_factor
        y_range = (ylim[1] - ylim[0]) * self.zoom_factor

        new_xlim = (x_center - x_range/2, x_center + x_range/2)

        # 限制不能超出原始范围太多
        if self.original_xlim and self.original_ylim:
            # X轴不能超出原始范围
            if new_xlim[0] < self.original_xlim[0]:
                new_xlim = (self.original_xlim[0], new_xlim[1])
            if new_xlim[1] > self.original_xlim[1]:
                new_xlim = (new_xlim[0], self.original_xlim[1])

        # 根据新的X轴范围计算实际的数据Y轴范围
        if self.df is not None and len(self.df) > 0:
            start_idx = max(0, int(new_xlim[0] + 0.5))
            end_idx = min(len(self.df) - 1, int(new_xlim[1] + 0.5))

            if start_idx <= end_idx:
                visible_data = self.df.iloc[start_idx:end_idx+1]

                # 计算所有相关数据的最高最低价
                all_prices = [visible_data['low'].min(), visible_data['high'].max()]

                # 考虑均线数据（支持大小写）
                ma_columns = [col for col in visible_data.columns if col.lower().startswith('ma')]
                for ma_col in ma_columns:
                    pass
                    if ma_col in visible_data.columns:
                        ma_data = visible_data[ma_col].dropna()
                        if len(ma_data) > 0:
                            all_prices.extend([ma_data.min(), ma_data.max()])

                actual_min_price = min(all_prices)
                actual_max_price = max(all_prices)

                # 为Y轴底部添加小边距
                price_range = actual_max_price - actual_min_price
                bottom_margin = price_range * 0.01
                new_ylim = (actual_min_price - bottom_margin, actual_max_price)

            else:
                # 备用方案
                new_ylim = (y_center - y_range/2, y_center + y_range/2)
                y_range_calc = new_ylim[1] - new_ylim[0]
                bottom_margin = y_range_calc * 0.01
                new_ylim = (new_ylim[0] - bottom_margin, new_ylim[1])
        else:
            # 备用方案
            new_ylim = (y_center - y_range/2, y_center + y_range/2)
            y_range_calc = new_ylim[1] - new_ylim[0]
            bottom_margin = y_range_calc * 0.01
            new_ylim = (new_ylim[0] - bottom_margin, new_ylim[1])

        ax.set_xlim(new_xlim)
        ax.set_ylim(new_ylim)

        # 重新设置Y轴刻度（传入实际数据范围，不包含边距）
        if self.df is not None and len(self.df) > 0:
            start_idx = max(0, int(new_xlim[0] + 0.5))
            end_idx = min(len(self.df) - 1, int(new_xlim[1] + 0.5))

            if start_idx <= end_idx:
                visible_data = self.df.iloc[start_idx:end_idx+1]
                all_prices = [visible_data['low'].min(), visible_data['high'].max()]
                ma_columns = [col for col in visible_data.columns if col.lower().startswith('ma')]
                for ma_col in ma_columns:
                    pass
                    if ma_col in visible_data.columns:
                        ma_data = visible_data[ma_col].dropna()
                        if len(ma_data) > 0:
                            all_prices.extend([ma_data.min(), ma_data.max()])
                actual_data_ylim = (min(all_prices), max(all_prices))
                self.update_y_axis_ticks(ax, actual_data_ylim)
            else:
                self.update_y_axis_ticks(ax, new_ylim)
        else:
            self.update_y_axis_ticks(ax, new_ylim)

        # 同步成交量图的X轴
        if len(self.axes) > 1:
            self.axes[1].set_xlim(new_xlim)

        self.draw()

    def reset_zoom(self):
        """还原缩放"""
        if not self.zoom_mode or not self.axes or len(self.axes) == 0:
            return

        if self.original_xlim and self.original_ylim:
            ax = self.axes[0]  # 主图轴
            ax.set_xlim(self.original_xlim)
            ax.set_ylim(self.original_ylim)

            # 重新设置Y轴刻度
            self.update_y_axis_ticks(ax, self.original_ylim)

            # 同步成交量图的X轴
            if len(self.axes) > 1:
                self.axes[1].set_xlim(self.original_xlim)

            self.draw()

        self.zoom_mode = False
        self.original_xlim = None
        self.original_ylim = None
        self.is_zoomed = False

    def is_zoomed_state(self):
        """检测是否处于缩放状态"""
        # 首先检查明确的缩放标志
        if self.zoom_mode or self.is_zoomed:
            return True

        # 检查是否有保存的原始范围（这表明进行过缩放操作）
        if self.original_xlim is not None and self.original_ylim is not None:
            pass
            if not self.axes or len(self.axes) == 0:
                return False

            ax = self.axes[0]
            current_xlim = ax.get_xlim()

            # 只检查X轴范围是否改变（X轴范围改变是用户主动缩放的明确标志）
            # Y轴会因为均线计算而动态调整，所以不检查Y轴
            x_changed = (abs(current_xlim[0] - self.original_xlim[0]) > 0.1 or
                        abs(current_xlim[1] - self.original_xlim[1]) > 0.1)
            return x_changed

        return False

    def reset_all_zoom(self):
        """还原所有类型的缩放（键盘缩放和鼠标框选缩放）"""
        if not self.axes or len(self.axes) == 0:
            return

        ax = self.axes[0]

        # 如果有保存的原始范围，重新计算包含均线的完整范围
        if self.original_xlim and self.original_ylim:
            # 重新计算包含所有均线的完整数据范围
            if self.df is not None and len(self.df) > 0:
                start_idx = max(0, int(self.original_xlim[0] + 0.5))
                end_idx = min(len(self.df) - 1, int(self.original_xlim[1] + 0.5))

                if start_idx <= end_idx:
                    visible_data = self.df.iloc[start_idx:end_idx+1]

                    # 计算所有相关数据的最高最低价
                    all_prices = [visible_data['low'].min(), visible_data['high'].max()]

                    # 考虑均线数据（支持大小写）
                    ma_columns = [col for col in visible_data.columns if col.lower().startswith('ma')]
                    for ma_col in ma_columns:
                        pass
                        if ma_col in visible_data.columns:
                            ma_data = visible_data[ma_col].dropna()
                            if len(ma_data) > 0:
                                all_prices.extend([ma_data.min(), ma_data.max()])

                    actual_min_price = min(all_prices)
                    actual_max_price = max(all_prices)

                    # 为Y轴底部添加小边距
                    price_range = actual_max_price - actual_min_price
                    bottom_margin = price_range * 0.01
                    adjusted_ylim = (actual_min_price - bottom_margin, actual_max_price)

                    ax.set_xlim(self.original_xlim)
                    ax.set_ylim(adjusted_ylim)

                    # 重新设置Y轴刻度（使用实际数据范围）
                    self.update_y_axis_ticks(ax, (actual_min_price, actual_max_price))

                    if ma_columns:
                        pass
                else:
                    # 备用方案：使用保存的范围
                    y_range = self.original_ylim[1] - self.original_ylim[0]
                    bottom_margin = y_range * 0.01
                    adjusted_ylim = (self.original_ylim[0] - bottom_margin, self.original_ylim[1])

                    ax.set_xlim(self.original_xlim)
                    ax.set_ylim(adjusted_ylim)
                    self.update_y_axis_ticks(ax, self.original_ylim)
            else:
                # 备用方案：使用保存的范围
                y_range = self.original_ylim[1] - self.original_ylim[0]
                bottom_margin = y_range * 0.01
                adjusted_ylim = (self.original_ylim[0] - bottom_margin, self.original_ylim[1])

                ax.set_xlim(self.original_xlim)
                ax.set_ylim(adjusted_ylim)
                self.update_y_axis_ticks(ax, self.original_ylim)

            # 同步成交量图的X轴
            if len(self.axes) > 1:
                self.axes[1].set_xlim(self.original_xlim)

        # 如果没有保存的原始范围，但有数据，还原到包含均线的完整数据范围
        elif self.df is not None and len(self.df) > 0:
            # 重新计算包含所有均线的完整数据范围
            data_xlim = (-0.5, len(self.df) - 0.5)

            # 计算所有相关数据的最高最低价
            all_prices = [self.df['low'].min(), self.df['high'].max()]

            # 考虑均线数据（支持大小写）
            ma_columns = [col for col in self.df.columns if col.lower().startswith('ma')]
            for ma_col in ma_columns:
                pass
                if ma_col in self.df.columns:
                    ma_data = self.df[ma_col].dropna()
                    if len(ma_data) > 0:
                        all_prices.extend([ma_data.min(), ma_data.max()])

            actual_min_price = min(all_prices)
            actual_max_price = max(all_prices)

            # 为Y轴底部添加小边距
            price_range = actual_max_price - actual_min_price
            bottom_margin = price_range * 0.01

            ax.set_xlim(data_xlim)
            ax.set_ylim(actual_min_price - bottom_margin, actual_max_price)

            # 重新设置Y轴刻度（使用实际数据范围）
            self.update_y_axis_ticks(ax, (actual_min_price, actual_max_price))

            # 同步成交量图的X轴
            if len(self.axes) > 1:
                self.axes[1].set_xlim(data_xlim)

            if ma_columns:
                pass

        # 重置状态
        self.zoom_mode = False
        self.is_zoomed = False

        self.draw()

    def reset_view_only(self):
        """只还原视图，不退出工具栏模式"""
        if not self.axes or len(self.axes) == 0:
            return

        ax = self.axes[0]

        # 如果有保存的原始范围，重新计算包含均线的完整范围
        if self.original_xlim and self.original_ylim:
            # 重新计算包含所有均线的完整数据范围
            if self.df is not None and len(self.df) > 0:
                start_idx = max(0, int(self.original_xlim[0] + 0.5))
                end_idx = min(len(self.df) - 1, int(self.original_xlim[1] + 0.5))

                if start_idx <= end_idx:
                    visible_data = self.df.iloc[start_idx:end_idx+1]

                    # 计算所有相关数据的最高最低价
                    all_prices = [visible_data['low'].min(), visible_data['high'].max()]

                    # 考虑均线数据（支持大小写）
                    ma_columns = [col for col in visible_data.columns if col.lower().startswith('ma')]
                    for ma_col in ma_columns:
                        if ma_col in visible_data.columns:
                            ma_data = visible_data[ma_col].dropna()
                            if len(ma_data) > 0:
                                all_prices.extend([ma_data.min(), ma_data.max()])

                    actual_min_price = min(all_prices)
                    actual_max_price = max(all_prices)

                    # 为Y轴底部添加小边距
                    price_range = actual_max_price - actual_min_price
                    bottom_margin = price_range * 0.01
                    adjusted_ylim = (actual_min_price - bottom_margin, actual_max_price)

                    ax.set_xlim(self.original_xlim)
                    ax.set_ylim(adjusted_ylim)

                    # 重新设置Y轴刻度（使用实际数据范围）
                    self.update_y_axis_ticks(ax, (actual_min_price, actual_max_price))

                else:
                    # 备用方案：使用保存的范围
                    y_range = self.original_ylim[1] - self.original_ylim[0]
                    bottom_margin = y_range * 0.01
                    adjusted_ylim = (self.original_ylim[0] - bottom_margin, self.original_ylim[1])

                    ax.set_xlim(self.original_xlim)
                    ax.set_ylim(adjusted_ylim)
                    self.update_y_axis_ticks(ax, self.original_ylim)
            else:
                # 备用方案：使用保存的范围
                y_range = self.original_ylim[1] - self.original_ylim[0]
                bottom_margin = y_range * 0.01
                adjusted_ylim = (self.original_ylim[0] - bottom_margin, self.original_ylim[1])

                ax.set_xlim(self.original_xlim)
                ax.set_ylim(adjusted_ylim)
                self.update_y_axis_ticks(ax, self.original_ylim)

            # 同步成交量图的X轴
            if len(self.axes) > 1:
                self.axes[1].set_xlim(self.original_xlim)

        # 如果没有保存的原始范围，但有数据，还原到包含均线的完整数据范围
        elif self.df is not None and len(self.df) > 0:
            # 重新计算包含所有均线的完整数据范围
            data_xlim = (-0.5, len(self.df) - 0.5)

            # 计算所有相关数据的最高最低价
            all_prices = [self.df['low'].min(), self.df['high'].max()]

            # 考虑均线数据（支持大小写）
            ma_columns = [col for col in self.df.columns if col.lower().startswith('ma')]
            for ma_col in ma_columns:
                if ma_col in self.df.columns:
                    ma_data = self.df[ma_col].dropna()
                    if len(ma_data) > 0:
                        all_prices.extend([ma_data.min(), ma_data.max()])

            actual_min_price = min(all_prices)
            actual_max_price = max(all_prices)

            # 为Y轴底部添加小边距
            price_range = actual_max_price - actual_min_price
            bottom_margin = price_range * 0.01

            ax.set_xlim(data_xlim)
            ax.set_ylim(actual_min_price - bottom_margin, actual_max_price)

            # 重新设置Y轴刻度（使用实际数据范围）
            self.update_y_axis_ticks(ax, (actual_min_price, actual_max_price))

            # 同步成交量图的X轴
            if len(self.axes) > 1:
                self.axes[1].set_xlim(data_xlim)

        # 注意：这里不重置 zoom_mode 和 is_zoomed 状态，保持工具栏模式
        self.draw()

    def recalculate_and_update_ticks(self, ax, xlim):
        """重新计算并更新刻度"""
        if self.df is not None and len(self.df) > 0:
            start_idx = max(0, int(xlim[0] + 0.5))
            end_idx = min(len(self.df) - 1, int(xlim[1] + 0.5))

            if start_idx <= end_idx:
                visible_data = self.df.iloc[start_idx:end_idx+1]

                # 计算所有相关数据的最高最低价
                all_prices = [visible_data['low'].min(), visible_data['high'].max()]

                # 考虑均线数据（支持大小写）
                ma_columns = [col for col in visible_data.columns if col.lower().startswith('ma')]
                for ma_col in ma_columns:
                    pass
                    if ma_col in visible_data.columns:
                        ma_data = visible_data[ma_col].dropna()
                        if len(ma_data) > 0:
                            all_prices.extend([ma_data.min(), ma_data.max()])

                actual_min_price = min(all_prices)
                actual_max_price = max(all_prices)

                actual_data_ylim = (actual_min_price, actual_max_price)
                self.update_y_axis_ticks(ax, actual_data_ylim)
                if ma_columns:
                    pass
                    for ma_col in ma_columns:
                        pass
                        if ma_col in visible_data.columns:
                            ma_data = visible_data[ma_col].dropna()
                            if len(ma_data) > 0:
                                pass

    def update_y_axis_ticks(self, ax, ylim):
        """更新刻度 - 基于传入的实际数据范围"""
        try:
            # 直接使用传入的ylim作为实际数据范围
            actual_min_price = ylim[0]
            actual_max_price = ylim[1]


            price_range = actual_max_price - actual_min_price

            if price_range > 0:
                # 生成5个刻度
                tick_count = 5
                tick_interval = price_range / (tick_count - 1)

                # 生成刻度位置
                ticks = []
                for i in range(tick_count):
                    tick_value = actual_min_price + i * tick_interval
                    ticks.append(tick_value)

                # 确保边界刻度的精确性
                ticks[0] = actual_min_price  # 确保第一个刻度是精确的最低价
                ticks[-1] = actual_max_price  # 确保最后一个刻度是精确的最高价

                # 设置刻度
                ax.set_yticks(ticks)

                # 格式化刻度标签
                tick_labels = [f'{tick:.2f}' for tick in ticks]
                ax.set_yticklabels(tick_labels)

                # 禁用自动边距调整，确保严格按照设置的范围显示
                ax.margins(0)

                # 清除旧的网格线
                self.clear_grid_lines()

                # 添加新的横轴线
                for i, tick in enumerate(ticks):
                    # 最低价刻度线使用稍微突出的样式，但位置保持准确
                    if i == 0:  # 最低价刻度线
                        hline = ax.axhline(y=tick, color='gray', linestyle='-', alpha=0.6, linewidth=0.8, zorder=2)
                    else:
                        hline = ax.axhline(y=tick, color='gray', linestyle='-', alpha=0.4, linewidth=0.5, zorder=1)
                    self.grid_lines.append(hline)  # 保存引用

            else:
                # 价格范围为0的特殊情况
                ax.set_yticks([actual_min_price])
                ax.set_yticklabels([f'{actual_min_price:.2f}'])

        except Exception as e:
            pass

    def clear_grid_lines(self):
        """清除所有网格线"""
        for line in self.grid_lines:
            pass
            try:
                line.remove()
            except Exception as e:
                pass
        self.grid_lines.clear()

    # 事件处理方法
    def on_mouse_move(self, event):
        """鼠标移动事件 - 完全按照原始版本"""
        if event.inaxes is None or self.df is None or not self.crosshair_enabled:
            return

        try:
            x_pos = event.xdata
            y_pos = event.ydata


            if x_pos is None or y_pos is None:
                return

            # 转换为数据索引
            data_index = int(round(x_pos))

            # 确保索引在有效范围内
            if data_index < 0:
                data_index = 0
            elif data_index >= len(self.df):
                data_index = len(self.df) - 1

            # 更新当前十字光标索引
            self.current_crosshair_index = data_index

            # 获取数据
            row_data = self.df.iloc[data_index]
            timestamp = self.df.index[data_index]

            # 调试：输出成交量信息
            if 'volume' in row_data:
                volume = row_data['volume']

            # 更新信息显示
            parent = self.parent()
            while parent and not hasattr(parent, 'update_info_display'):
                parent = parent.parent()

            if parent and hasattr(parent, 'update_info_display'):
                parent.update_info_display(timestamp, row_data)

            # 更新十字光标
            self.update_crosshair(x_pos, y_pos, event.inaxes)

        except Exception as e:
            pass

    def on_mouse_click(self, event):
        """鼠标点击事件"""
        if event.dblclick:
            self.toggle_crosshair()

    def on_scroll(self, event):
        """鼠标滚轮事件 - 切换股票"""
        if event.inaxes is None:
            return

        try:
            # 获取父窗口
            parent = self.parent()
            while parent and not hasattr(parent, 'switch_stock'):
                parent = parent.parent()

            if parent and hasattr(parent, 'switch_stock'):
                pass
                if event.button == 'up':
                    parent.switch_stock(-1)  # 上一只股票
                elif event.button == 'down':
                    parent.switch_stock(1)   # 下一只股票

        except Exception as e:
            pass

    def on_key_press(self, event):
        """按键事件"""
        # 确保canvas有焦点（Qt版本）
        try:
            self.setFocus()
        except:
            pass

        if event.key == 'c':
            self.toggle_crosshair()
        elif event.key == 'escape':
            # 防止ESC键重复处理
            if self.esc_processing:
                return

            self.esc_processing = True
            try:
                # 优先级0: 如果有之前的视图，返回原视图
                parent = self.parent()
                while parent and not hasattr(parent, 'return_to_previous_view'):
                    parent = parent.parent()

                if parent and hasattr(parent, 'return_to_previous_view'):
                    if parent.return_to_previous_view():
                        return  # 成功返回原视图，不继续处理其他ESC逻辑

                # 处于工具栏模式（放大镜/拖拽）时的特殊处理
                if self.is_toolbar_zoom_mode() or self.is_toolbar_pan_mode():
                    # 如果在工具栏模式下进行过缩放，第一次ESC先还原视图（但保持工具栏模式）
                    if self.toolbar_has_zoomed:
                        self.reset_view_only()
                        self.toolbar_has_zoomed = False  # 重置标志，下次ESC将退出工具栏模式
                        return  # 重要：防止继续执行后面的elif分支
                    # 如果没有缩放或已经还原，第二次ESC退出工具栏模式
                    else:
                        success = self.exit_toolbar_modes()
                        if success:
                            # 停止工具栏监控
                            if self.toolbar_check_timer.isActive():
                                self.toolbar_check_timer.stop()
                            self.last_toolbar_mode = None
                            self.toolbar_has_zoomed = False  # 重置标志
                        return  # 重要：防止继续执行后面的elif分支
                # 不在工具栏模式时的原有逻辑
                elif self.is_zoomed_state():
                    # 优先级1: 还原缩放
                    self.reset_all_zoom()
                # 优先级2: 关闭十字光标
                elif self.crosshair_enabled:
                    self.toggle_crosshair()
            finally:
                # 使用QTimer延迟重置标志，避免同一事件循环中的重复处理
                QTimer.singleShot(100, lambda: setattr(self, 'esc_processing', False))
        elif event.key == 'left' and self.crosshair_enabled and self.df is not None:
            # 防止左键重复处理
            if self.key_processing:
                return
            self.key_processing = True
            try:
                self.move_crosshair(-1)
            finally:
                # 延迟重置标志，防止快速连续按键
                QTimer.singleShot(100, lambda: setattr(self, 'key_processing', False))
        elif event.key == 'right' and self.crosshair_enabled and self.df is not None:
            # 防止右键重复处理
            if self.key_processing:
                return
            self.key_processing = True
            try:
                self.move_crosshair(1)
            finally:
                # 延迟重置标志，防止快速连续按键
                QTimer.singleShot(100, lambda: setattr(self, 'key_processing', False))
        elif event.key == 'up':
            # 防止上下键重复处理
            if self.key_processing:
                return
            self.key_processing = True
            try:
                # 检查是否处于放大镜模式
                if self.is_toolbar_zoom_mode():
                    self.zoom_in()
                else:
                    self.switch_to_previous_stock()
            finally:
                # 延迟重置标志，防止快速连续按键
                QTimer.singleShot(200, lambda: setattr(self, 'key_processing', False))
        elif event.key == 'down':
            # 防止上下键重复处理
            if self.key_processing:
                return
            self.key_processing = True
            try:
                # 检查是否处于放大镜模式
                if self.is_toolbar_zoom_mode():
                    self.zoom_out()
                else:
                    self.switch_to_next_stock()
            finally:
                # 延迟重置标志，防止快速连续按键
                QTimer.singleShot(200, lambda: setattr(self, 'key_processing', False))
        elif event.key == 'q':
            # Q键：设置15m数据库中对应股票的signal
            # 防止重复处理
            if hasattr(self, 'q_processing') and self.q_processing:
                return

            self.q_processing = True
            print("Q键被按下，调用set_15m_signal()")

            try:
                # 获取父窗口（主程序）
                parent = self.parent()
                while parent and not hasattr(parent, 'set_15m_signal'):
                    parent = parent.parent()

                if parent and hasattr(parent, 'set_15m_signal'):
                    parent.set_15m_signal()
                else:
                    print("未找到父窗口的set_15m_signal方法")
            finally:
                # 延迟重置标志，防止快速连续按键
                QTimer.singleShot(500, lambda: setattr(self, 'q_processing', False))

    def switch_to_previous_stock(self):
        """切换到上一只股票"""
        try:
            # 获取父窗口（主程序）
            parent = self.parent()
            while parent and not hasattr(parent, 'switch_stock'):
                parent = parent.parent()

            if parent and hasattr(parent, 'switch_stock'):
                parent.switch_stock(-1)  # 上一只股票
        except Exception as e:
            pass

    def switch_to_next_stock(self):
        """切换到下一只股票"""
        try:
            # 获取父窗口（主程序）
            parent = self.parent()
            while parent and not hasattr(parent, 'switch_stock'):
                parent = parent.parent()

            if parent and hasattr(parent, 'switch_stock'):
                parent.switch_stock(1)  # 下一只股票
        except Exception as e:
            pass

    def on_canvas_click(self, event):
        """Canvas点击事件 - 确保获得焦点"""
        try:
            # 确保canvas获得焦点，这样键盘事件才能被接收
            self.setFocus()

            # 开始监控工具栏状态
            self.start_toolbar_monitoring()
        except Exception as e:
            pass

    def start_toolbar_monitoring(self):
        """开始监控工具栏状态变化"""
        try:
            pass
            if self.toolbar_check_timer.isActive():
                self.toolbar_check_timer.stop()

            self.check_toolbar_status()
        except Exception as e:
            pass

    def check_toolbar_status(self):
        """定期检查工具栏状态"""
        try:
            toolbar = self.get_toolbar()
            if toolbar and hasattr(toolbar, 'mode'):
                current_mode = toolbar.mode

                # 如果模式发生变化，输出日志
                if current_mode != self.last_toolbar_mode:
                    pass
                    if current_mode == 'zoom rect':
                        pass
                    elif current_mode == 'pan/zoom':
                        pass
                    elif current_mode == '' and self.last_toolbar_mode:
                        pass

                    self.last_toolbar_mode = current_mode

                # 如果处于工具栏模式，继续监控
                if current_mode in ['zoom rect', 'pan/zoom']:
                    self.toolbar_check_timer.start(100)  # 100ms间隔检查
                else:
                    self.toolbar_check_timer.stop()
            else:
                self.toolbar_check_timer.stop()

        except Exception as e:
            self.toolbar_check_timer.stop()

    def on_resize(self, event):
        """窗口大小变化事件"""
        # 强制重绘
        if self.df is not None:
            self.draw()

    def get_toolbar(self):
        """获取matplotlib工具栏"""
        try:
            # 在Qt应用中，工具栏通常是父窗口的一部分
            parent = self.parent()
            while parent:
                # 查找NavigationToolbar
                for child in parent.findChildren(NavigationToolbar):
                    return child
                parent = parent.parent()
            return None
        except Exception as e:
            return None

    def is_toolbar_zoom_mode(self):
        """检测是否处于工具栏放大镜模式"""
        try:
            toolbar = self.get_toolbar()
            if toolbar and hasattr(toolbar, 'mode'):
                is_zoom = toolbar.mode == 'zoom rect'
                if is_zoom:
                    pass
                return is_zoom
            return False
        except Exception as e:
            return False

    def is_toolbar_pan_mode(self):
        """检测是否处于工具栏拖拽模式"""
        try:
            toolbar = self.get_toolbar()
            if toolbar and hasattr(toolbar, 'mode'):
                is_pan = toolbar.mode == 'pan/zoom'
                if is_pan:
                    pass
                return is_pan
            return False
        except Exception as e:
            return False

    def exit_toolbar_modes(self):
        """退出工具栏的所有模式"""
        try:
            toolbar = self.get_toolbar()
            if toolbar:
                current_mode = getattr(toolbar, 'mode', None)

                success = False

                # 方法1: 调用对应的工具栏方法
                if current_mode == 'zoom rect' and hasattr(toolbar, 'zoom'):
                    toolbar.zoom()
                    success = True
                elif current_mode == 'pan/zoom' and hasattr(toolbar, 'pan'):
                    toolbar.pan()
                    success = True

                # 方法2: 强制重置工具栏状态
                if not success and current_mode and current_mode != '':
                    pass
                    try:
                        # 重置工具栏的内部状态
                        if hasattr(toolbar, '_active'):
                            toolbar._active = None
                        if hasattr(toolbar, 'mode'):
                            toolbar.mode = ''
                        if hasattr(toolbar, '_idPress'):
                            toolbar._idPress = None
                        if hasattr(toolbar, '_idRelease'):
                            toolbar._idRelease = None

                        # 更新工具栏按钮状态
                        if hasattr(toolbar, '_update_buttons_checked'):
                            toolbar._update_buttons_checked()

                        success = True
                    except Exception as e2:
                        pass

                # 方法3: 通过按钮状态重置
                if not success:
                    pass
                    try:
                        # 尝试找到并重置按钮状态
                        if hasattr(toolbar, '_buttons'):
                            pass
                            for name, button in toolbar._buttons.items():
                                pass
                                if hasattr(button, 'var') and button.var.get():
                                    button.var.set(False)
                                    success = True
                    except Exception as e3:
                        pass

                # 强制刷新canvas
                if success:
                    pass
                    try:
                        self.draw_idle()
                    except:
                        pass

                return success
            else:
                return False
        except Exception as e:
            return False

    def connect_axis_events(self):
        """连接轴范围变化事件，处理工具栏缩放"""
        if self.axes and len(self.axes) > 0:
            ax = self.axes[0]  # 主图轴
            # 连接X轴和Y轴范围变化事件
            ax.callbacks.connect('xlim_changed', self.on_xlim_changed)
            ax.callbacks.connect('ylim_changed', self.on_ylim_changed)

    def on_xlim_changed(self, ax):
        """X轴范围变化回调 - 重新计算Y轴范围和刻度"""
        if self.df is None or len(self.df) == 0:
            return

        try:
            # 获取新的X轴范围
            xlim = ax.get_xlim()

            # 检查是否是用户主动缩放（X轴范围改变）
            if self.original_xlim is None:
                # 第一次设置原始范围
                self.save_original_limits()
            else:
                # 检查X轴是否发生了显著变化
                x_changed = (abs(xlim[0] - self.original_xlim[0]) > 0.1 or
                           abs(xlim[1] - self.original_xlim[1]) > 0.1)
                if x_changed:
                    # 用户进行了缩放操作
                    self.is_zoomed = True
                    # 如果在工具栏模式下，设置工具栏缩放标志
                    if self.is_toolbar_zoom_mode() or self.is_toolbar_pan_mode():
                        self.toolbar_has_zoomed = True

            # 根据新的X轴范围计算实际的数据Y轴范围
            start_idx = max(0, int(xlim[0] + 0.5))
            end_idx = min(len(self.df) - 1, int(xlim[1] + 0.5))

            if start_idx <= end_idx:
                visible_data = self.df.iloc[start_idx:end_idx+1]

                # 计算所有相关数据的最高最低价
                all_prices = [visible_data['low'].min(), visible_data['high'].max()]

                # 考虑均线数据（支持大小写）
                ma_columns = [col for col in visible_data.columns if col.lower().startswith('ma')]
                for ma_col in ma_columns:
                    pass
                    if ma_col in visible_data.columns:
                        ma_data = visible_data[ma_col].dropna()
                        if len(ma_data) > 0:
                            all_prices.extend([ma_data.min(), ma_data.max()])

                actual_min_price = min(all_prices)
                actual_max_price = max(all_prices)

                if ma_columns:
                    pass
                    for ma_col in ma_columns:
                        pass
                        if ma_col in visible_data.columns:
                            ma_data = visible_data[ma_col].dropna()
                            if len(ma_data) > 0:
                                pass

                # 为Y轴底部添加小边距
                price_range = actual_max_price - actual_min_price
                bottom_margin = price_range * 0.01
                new_ylim = (actual_min_price - bottom_margin, actual_max_price)

                # 更新Y轴范围（这会触发ylim_changed，但我们需要避免无限循环）
                current_ylim = ax.get_ylim()
                if (abs(current_ylim[0] - new_ylim[0]) > 1e-6 or
                    abs(current_ylim[1] - new_ylim[1]) > 1e-6):
                    ax.set_ylim(new_ylim)

                # 同步成交量图的X轴范围
                if len(self.axes) > 1 and ax == self.axes[0]:  # 只有主图轴变化时才同步
                    self.axes[1].set_xlim(xlim)

        except Exception as e:
            pass

    def on_ylim_changed(self, ax):
        """Y轴范围变化回调 - 更新刻度"""
        if self.df is None or len(self.df) == 0:
            return

        try:
            # 获取当前X轴显示范围，重新计算实际数据范围
            xlim = ax.get_xlim()
            start_idx = max(0, int(xlim[0] + 0.5))
            end_idx = min(len(self.df) - 1, int(xlim[1] + 0.5))

            if start_idx <= end_idx:
                visible_data = self.df.iloc[start_idx:end_idx+1]

                # 计算所有相关数据的最高最低价
                all_prices = [visible_data['low'].min(), visible_data['high'].max()]

                # 考虑均线数据（支持大小写）
                ma_columns = [col for col in visible_data.columns if col.lower().startswith('ma')]
                for ma_col in ma_columns:
                    pass
                    if ma_col in visible_data.columns:
                        ma_data = visible_data[ma_col].dropna()
                        if len(ma_data) > 0:
                            all_prices.extend([ma_data.min(), ma_data.max()])

                actual_data_ylim = (min(all_prices), max(all_prices))

                # 更新刻度（基于实际数据范围）
                self.update_y_axis_ticks(ax, actual_data_ylim)
            else:
                # 备用方案
                ylim = ax.get_ylim()
                self.update_y_axis_ticks(ax, ylim)

        except Exception as e:
            pass


class StockViewerWindow(QMainWindow):
    """完整的股票看盘软件主窗口"""

    def __init__(self):
        super().__init__()
        self.data_dict = {}
        self.current_key = None

        # 防抖机制相关变量
        self.scroll_timer = None
        self.pending_load_key = None
        self.is_loading = False  # 防止重复加载
        self._is_scroll_triggered = False  # 区分滚轮和点击

        # 15m数据库路径和内存数据
        self.min15_db_path = "F主板0515min15.h5"
        self.min15_data_cache = {}  # 内存中的15m数据缓存
        self.previous_view_key = None  # 保存切换前的视图

        self.setup_ui()

    def keyPressEvent(self, event):
        """主窗口键盘事件处理"""
        print(f"MainWindow keyPressEvent: {event.key()}, text: {event.text()}")

        if event.key() == Qt.Key.Key_Q:
            print("主窗口捕获到Q键，调用set_15m_signal()")
            self.set_15m_signal()
        else:
            super().keyPressEvent(event)

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("股票看盘软件 - 增强版 (双击开启十字光标，点击放大镜后按↑↓缩放，Esc还原)")
        self.setGeometry(100, 100, 1600, 1000)

        # 创建状态栏
        self.statusBar().showMessage("就绪 - 双击图表开启十字光标，点击放大镜后按↑↓键缩放，ESC键先还原再退出模式")

        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QHBoxLayout()

        # 左侧控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel, 0)

        # 右侧图表区域
        chart_container = QWidget()
        chart_layout = QVBoxLayout()

        self.chart_widget = ManualKLineCanvas(self)
        self.toolbar = CustomNavigationToolbar(self.chart_widget, self)

        chart_layout.addWidget(self.chart_widget)
        chart_layout.addWidget(self.toolbar)
        chart_container.setLayout(chart_layout)

        main_layout.addWidget(chart_container, 1)

        central_widget.setLayout(main_layout)

        # 创建菜单栏
        self.create_menu_bar()

        self.statusBar().showMessage("就绪 - 请选择HDF5文件 (点击放大镜后按↑↓键缩放，ESC键先还原再退出模式)")

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件')

        open_action = QAction('打开HDF5文件', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.select_file)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

    def create_control_panel(self):
        """创建左侧控制面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        panel.setMaximumWidth(350)
        panel.setMinimumWidth(300)

        layout = QVBoxLayout()

        # 文件选择区域
        file_group = QFrame()
        file_layout = QVBoxLayout()

        file_layout.addWidget(QLabel("HDF5数据文件:"))

        file_select_layout = QHBoxLayout()
        self.file_edit = QLineEdit()
        self.file_edit.setReadOnly(True)
        file_btn = QPushButton("选择文件")
        file_btn.clicked.connect(self.select_file)
        file_select_layout.addWidget(self.file_edit)
        file_select_layout.addWidget(file_btn)
        file_layout.addLayout(file_select_layout)

        # 15m数据按钮
        min15_btn_layout = QHBoxLayout()
        self.min15_data_btn = QPushButton("15m数据")
        self.min15_data_btn.clicked.connect(self.load_15m_data)
        self.min15_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        min15_btn_layout.addWidget(self.min15_data_btn)
        min15_btn_layout.addStretch()  # 添加弹性空间，让按钮靠左
        file_layout.addLayout(min15_btn_layout)

        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # 股票选择区域
        stock_group = QFrame()
        stock_layout = QVBoxLayout()

        # 创建带数量提示的标签
        self.stock_label = QLabel("选择股票: (请先选择HDF文件)")
        stock_layout.addWidget(self.stock_label)
        self.stock_list = AutoScrollListWidget()
        self.stock_list.setMaximumHeight(200)  # 限制高度
        self.stock_list.setMinimumHeight(150)  # 最小高度

        # 启用多选模式
        self.stock_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)

        # 启用拖拽移动
        self.stock_list.setDragDropMode(QListWidget.DragDropMode.InternalMove)

        # 启用右键菜单
        self.stock_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.stock_list.customContextMenuRequested.connect(self.show_stock_list_context_menu)

        # 设置列表框样式，紧凑行间距，修复悬停问题
        self.stock_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 4px;
                font-size: 12px;
                outline: none;
                show-decoration-selected: 1;
            }
            QListWidget::item {
                padding: 3px 8px;
                border-bottom: 1px solid #eee;
                height: 18px;
                margin: 0px;
            }
            QListWidget::item:selected {
                background-color: #0078d4 !important;
                color: white !important;
                font-weight: bold;
            }
            QListWidget::item:selected:hover {
                background-color: #0078d4 !important;
                color: white !important;
            }
            QListWidget::item:selected:focus {
                background-color: #0078d4 !important;
                color: white !important;
            }
            QListWidget::item:hover:!selected {
                background-color: #f5f5f5;
            }
        """)

        self.stock_list.currentItemChanged.connect(self.on_stock_list_changed)
        stock_layout.addWidget(self.stock_list)

        stock_group.setLayout(stock_layout)
        layout.addWidget(stock_group)

        # 信息显示区域
        info_group = QFrame()
        info_layout = QVBoxLayout()

        info_layout.addWidget(QLabel("股票信息:"))
        self.info_display = QTextEdit()
        self.info_display.setFixedHeight(500)     # 固定高度500px，合适的信息显示区域
        self.info_display.setReadOnly(True)
        self.info_display.setFont(QFont("Consolas", 9))
        info_layout.addWidget(self.info_display)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # 删除保存按钮

        # 删除K线颜色说明

        layout.addStretch()
        panel.setLayout(layout)
        return panel

    def select_file(self):
        """选择HDF5文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择HDF5文件", "", "HDF5文件 (*.h5 *.hdf5);;所有文件 (*)")

        if file_path:
            self.load_hdf_file(file_path)

    def load_15m_data(self):
        """加载15m数据库到内存 - 仅用于Q键功能，不切换界面"""
        min15_path = "F主板0515min15.h5"
        if not os.path.exists(min15_path):
            QMessageBox.warning(self, "警告", f"15m数据库文件不存在: {min15_path}")
            # 如果文件不存在，让用户选择15m数据库文件路径
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择15m数据库文件", "", "HDF5文件 (*.h5 *.hdf5);;所有文件 (*)")
            if file_path:
                min15_path = file_path
                self.min15_db_path = file_path
            else:
                return

        try:
            # 加载15m数据到内存缓存
            with pd.HDFStore(min15_path, 'r') as store:
                # 获取所有节点
                keys = store.keys()
                stock_keys = [k for k in keys if '_15m' in k]

                # 清空之前的缓存
                self.min15_data_cache.clear()

                # 加载所有15m股票数据到内存
                for key in stock_keys:
                    df = store.get(key)
                    # 确保有signal列
                    if 'signal' not in df.columns:
                        df['signal'] = False
                    self.min15_data_cache[key] = df.copy()

                QMessageBox.information(self, "成功",
                    f"已将15m数据加载到内存\n"
                    f"数据库: {min15_path}\n"
                    f"股票数量: {len(stock_keys)}\n"
                    f"现在可以使用Q键设置信号了\n"
                    f"⚠️ 信号只在内存中，程序关闭后会丢失")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载15m数据失败: {str(e)}")

    def set_15m_signal(self):
        """设置15m数据库中对应股票的signal"""
        try:
            print("set_15m_signal() 方法被调用")
            print(f"current_key: {getattr(self, 'current_key', 'None')}")

            if not hasattr(self, 'current_key') or not self.current_key:
                print("没有选择股票，显示警告")
                QMessageBox.warning(self, "警告", "请先选择一只股票")
                return

            print("开始处理15m信号设置...")

            # 检查是否已加载15m数据到内存
            if not hasattr(self, 'min15_data_cache') or not self.min15_data_cache:
                QMessageBox.warning(self, "警告", "请先点击'15m数据'按钮加载数据到内存")
                return

            # 从当前节点名称中提取股票代码和日期
            # LT.h5格式: /stock_000627_60m_2024-09-12 1500
            # 15m数据库格式: /stock_000627_15m

            # 解析当前节点名称
            current_node = self.current_key.strip('/')
            parts = current_node.split('_')

            if len(parts) < 4:
                QMessageBox.warning(self, "警告", "无法解析当前股票节点格式")
                return

            stock_code = parts[1]  # 提取股票代码
            date_part = parts[3].split(' ')[0]  # 提取日期部分，去掉时间

            # 构建15m数据库中的节点名称
            min15_node = f"/stock_{stock_code}_15m"

            # 检查内存中是否有该股票数据
            if min15_node not in self.min15_data_cache:
                QMessageBox.warning(self, "警告", f"内存中未找到股票: {stock_code}")
                return

            # 从内存中获取数据
            df_15m = self.min15_data_cache[min15_node]

            # 查找指定日期15:00的数据行
            target_date = pd.to_datetime(date_part)
            target_datetime = target_date.replace(hour=15, minute=0)

            # 查找最接近15:00的时间点
            if target_datetime in df_15m.index:
                target_idx = target_datetime
            else:
                # 如果没有精确的15:00，查找当天最接近15:00的时间
                same_day_data = df_15m[df_15m.index.date == target_date.date()]
                if same_day_data.empty:
                    QMessageBox.warning(self, "警告", f"15m数据中未找到日期 {date_part} 的数据")
                    return

                # 找到最接近15:00的时间点
                time_diff = abs(same_day_data.index.hour * 60 + same_day_data.index.minute - 15 * 60)
                target_idx = same_day_data.index[time_diff.argmin()]

            # 按照生成策略数据库的方式处理数据不足情况
            pos = df_15m.index.get_loc(target_idx)

            # 使用与生成策略数据库相同的逻辑处理数据边界
            start = max(0, pos - 115)
            end = min(len(df_15m) - 1, pos + 116)

            # 在内存中设置signal为True
            df_15m.loc[target_idx, 'signal'] = True

            # 提取对应的K线数据段
            df_sub = df_15m.iloc[start:end + 1].copy()

            # 计算实际生成的K线信息
            total_klines = end - start + 1
            signal_position_in_data = pos - start + 1  # signal在实际数据中的位置

            # 判断数据是否完整
            is_complete = (start == pos - 115) and (end == pos + 116)
            before_count = pos - start
            after_count = end - pos

            # 生成新的节点key，格式与LT数据库一致
            if isinstance(target_idx, pd.Timestamp):
                time_str = target_idx.strftime("%Y-%m-%d %H%M")
            else:
                time_str = str(target_idx).replace(':', '')

            new_key = f"/stock_{stock_code}_15m_{time_str}"

            # 生成图表标题（删除"5分钟线 前复权"等无关信息）
            # 从15m数据中获取股票名称并清理
            stock_name = stock_code
            if len(df_15m.columns) > 0:
                first_col = str(df_15m.columns[0])
                # 清理股票名称，删除"5分钟线"、"前复权"等无关信息
                cleaned_name = first_col.replace("5分钟线", "").replace("前复权", "").replace("分钟线", "").strip()
                if cleaned_name and cleaned_name != stock_code:
                    stock_name = cleaned_name

            # 生成统一的显示名称，用于图表标题和列表显示
            display_name = f"{stock_name} 15分钟_{target_idx.strftime('%Y-%m-%d %H:%M') if isinstance(target_idx, pd.Timestamp) else str(target_idx)}"

            # 保存当前视图，以便ESC键返回
            self.previous_view_key = self.current_key

            # 将新数据添加到数据字典中（使用统一的显示名称作为title）
            self.data_dict[new_key] = (df_sub, display_name)

            # 更新股票列表 - 添加新的15分钟图表项目（使用统一的显示名称）
            item = QListWidgetItem(display_name)
            item.setData(Qt.ItemDataRole.UserRole, new_key)

            # 设置新生成条目的字体颜色为橙黄色
            from PyQt6.QtGui import QColor
            item.setForeground(QColor(255, 165, 0))  # 橙黄色字体

            self.stock_list.addItem(item)

            # 自动切换到新生成的15分钟图表
            self.current_key = new_key
            self.load_stock_data(new_key)

            # 在股票列表中选中新项目
            for i in range(self.stock_list.count()):
                item = self.stock_list.item(i)
                if item.data(Qt.ItemDataRole.UserRole) == new_key:
                    self.stock_list.setCurrentItem(item)
                    break

            # 构建详细的状态信息
            status_info = []
            if is_complete:
                status_info.append("✅ 数据完整，符合标准232根K线格式")
                status_info.append(f"Signal位置: 第116根K线")
            else:
                status_info.append("⚠️ 数据不完整，已自动调整范围")
                status_info.append(f"Signal前: {before_count}根K线（标准115根）")
                status_info.append(f"Signal后: {after_count}根K线（标准116根）")
                status_info.append(f"Signal位置: 第{signal_position_in_data}根K线")

            # 显示成功信息（简化版，因为已经切换到图表了）
            self.statusBar().showMessage(
                f"已生成15分钟图表: {stock_code} {target_idx} | "
                f"K线数: {total_klines} | "
                f"{'完整数据' if is_complete else '数据已调整'} | "
                f"按ESC返回原视图")

        except Exception as e:
            print(f"set_15m_signal 发生异常: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"设置15m信号时出错: {str(e)}")
        except:
            print("set_15m_signal 发生未知异常")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", "设置15m信号时发生未知错误")

    def return_to_previous_view(self):
        """返回到之前的视图"""
        if hasattr(self, 'previous_view_key') and self.previous_view_key:
            # 检查之前的视图是否还存在
            if self.previous_view_key in self.data_dict:
                # 切换回原视图
                self.current_key = self.previous_view_key
                self.load_stock_data(self.previous_view_key)

                # 在股票列表中选中原项目
                # 先清除所有选中状态，确保只有原项目被选中
                self.stock_list.clearSelection()
                for i in range(self.stock_list.count()):
                    item = self.stock_list.item(i)
                    if item.data(Qt.ItemDataRole.UserRole) == self.previous_view_key:
                        self.stock_list.setCurrentItem(item)
                        break

                # 清除previous_view_key
                self.previous_view_key = None

                # 更新状态栏
                self.statusBar().showMessage("已返回原视图")

                return True
            else:
                self.statusBar().showMessage("原视图已不存在")
                self.previous_view_key = None
                return False
        return False

    def show_stock_list_context_menu(self, position):
        """显示股票列表右键菜单"""
        if self.stock_list.itemAt(position) is None and len(self.stock_list.selectedItems()) == 0:
            return

        from PyQt6.QtWidgets import QMenu
        menu = QMenu(self)

        # 获取选中的项目
        selected_items = self.stock_list.selectedItems()

        if selected_items:
            # 批量穿透操作
            batch_penetrate_action = menu.addAction("批量穿透")
            batch_penetrate_action.triggered.connect(self.batch_generate_15m_charts)

            menu.addSeparator()

            # 智能排列操作
            distribute_action = menu.addAction("分布排列")
            distribute_action.triggered.connect(self.distribute_items)

            gather_bottom_action = menu.addAction("底部汇集")
            gather_bottom_action.triggered.connect(self.gather_items_to_bottom)

            menu.addSeparator()

            # 删除操作
            delete_action = menu.addAction("删除")
            delete_action.triggered.connect(self.delete_selected_items)

            menu.addSeparator()

            # 导出操作
            export_action = menu.addAction("导出到HDF文件")
            export_action.triggered.connect(self.export_selected_items)

        # 清空操作（总是可用）
        menu.addSeparator()
        clear_action = menu.addAction("清空")
        clear_action.triggered.connect(self.clear_stock_list)

        # 显示菜单
        menu.exec(self.stock_list.mapToGlobal(position))

    def load_hdf_file(self, file_path):
        """加载HDF5文件"""
        try:
            self.data_dict = {}
            with pd.HDFStore(file_path, 'r') as store:
                keys = store.keys()
                for key in keys:
                    pass
                    try:
                        df = store.get(key)
                        # 获取标题信息
                        title = key.replace('/', '')
                        storer = store.get_storer(key)
                        if storer is not None and hasattr(storer.attrs, 'title'):
                            title = storer.attrs.title
                        self.data_dict[key] = (df, title)
                    except Exception as e:
                        continue

            if not self.data_dict:
                QMessageBox.warning(self, "警告", "未找到有效的数据表")
                return

            # 更新文件路径显示
            self.file_edit.setText(file_path)

            # 更新股票选择列表框
            # 临时断开信号连接，避免重复触发
            self.stock_list.currentItemChanged.disconnect()
            self.stock_list.clear()

            # 添加股票到列表框
            for key in sorted(self.data_dict.keys()):
                _, title = self.data_dict[key]
                display_name = title if title else key.replace('/', '')

                item = QListWidgetItem(display_name)
                item.setData(Qt.ItemDataRole.UserRole, key)  # 存储key作为数据
                self.stock_list.addItem(item)

            # 删除保存按钮相关代码

            # 更新股票选择标签，显示数量
            stock_count = len(self.data_dict)
            self.stock_label.setText(f"选择股票: (共{stock_count}只)")

            self.statusBar().showMessage(f"已加载 {len(self.data_dict)} 只股票数据")

            # 自动选择第一只股票
            if self.data_dict:
                first_key = list(self.data_dict.keys())[0]
                # 设置当前选择但不触发事件
                self.stock_list.setCurrentRow(0)
                # 手动加载第一只股票
                self.load_stock_data(first_key)

            # 重新连接信号
            self.stock_list.currentItemChanged.connect(self.on_stock_list_changed)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载文件失败:\n{str(e)}")
            self.statusBar().showMessage("加载失败")
            # 重置股票标签
            self.stock_label.setText("选择股票: (请先选择HDF文件)")

    def on_stock_list_changed(self, current, previous):
        """股票列表选择改变事件 - 简化处理逻辑"""
        if current:
            key = current.data(Qt.ItemDataRole.UserRole)
            if key and key in self.data_dict:
                pass

                # 检查是否是滚轮触发的延迟加载
                if hasattr(self, '_is_scroll_triggered') and self._is_scroll_triggered:
                    self._is_scroll_triggered = False  # 重置标志
                else:
                    # 取消任何待执行的延迟加载
                    if self.scroll_timer and self.scroll_timer.isActive():
                        self.scroll_timer.stop()

                    if not self.is_loading:
                        self.is_loading = True
                        try:
                            self.load_stock_data(key)
                        finally:
                            self.is_loading = False

    def on_stock_changed(self, text):
        """股票选择改变事件 - 保留兼容性"""
        # 这个方法保留用于向后兼容，但现在主要使用on_stock_list_changed
        pass

    def load_stock_data(self, key):
        """加载指定股票的数据"""

        # 防止重复加载
        if self.is_loading and key == self.current_key:
            return

        if key not in self.data_dict:
            return

        try:
            df, title = self.data_dict[key]
            self.current_key = key


            # 确保数据有datetime索引
            if not isinstance(df.index, pd.DatetimeIndex):
                pass
                if 'datetime' in df.columns:
                    df = df.set_index('datetime')
                elif df.index.name == 'datetime':
                    df.index = pd.to_datetime(df.index)
                else:
                    # 尝试将索引转换为datetime
                    try:
                        df.index = pd.to_datetime(df.index)
                    except:
                        QMessageBox.warning(self, "警告", f"无法解析 {key} 的时间索引")
                        return

            # 检查必要的列
            required_cols = ['open', 'high', 'low', 'close']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                QMessageBox.warning(self, "警告", f"数据缺少必要列: {missing_cols}")
                return

            # 使用存储在data_dict中的title，确保与列表显示名称一致
            display_title = title if title else key.replace('/', '')

            self.chart_widget.plot_stock_data_double_buffer(df, display_title)

            self.statusBar().showMessage(f"已加载股票: {display_title} ({len(df)} 条记录)")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载股票数据失败:\n{str(e)}")
            self.statusBar().showMessage("加载股票数据失败")

    def switch_stock(self, direction):
        """切换股票 - 滚轮事件调用，使用防抖机制"""
        if not self.data_dict:
            return

        try:
            # 获取当前选中的行
            current_row = self.stock_list.currentRow()
            total_rows = self.stock_list.count()

            if total_rows == 0:
                return

            # 计算新的行索引
            new_row = (current_row + direction) % total_rows


            # 设置滚轮触发标志
            self._is_scroll_triggered = True

            # 立即更新列表框选择 - 蓝色选中条立即移动
            # 先清除所有选中状态，确保只有一个项目被选中
            self.stock_list.clearSelection()
            self.stock_list.setCurrentRow(new_row)

            # 获取新选中项的key
            new_item = self.stock_list.item(new_row)
            if new_item:
                new_key = new_item.data(Qt.ItemDataRole.UserRole)

                # 使用防抖机制延迟加载图表
                self.schedule_delayed_load(new_key, new_item.text())

        except Exception as e:
            import traceback
            traceback.print_exc()

    def schedule_delayed_load(self, key, title):
        """安排延迟加载 - 防抖机制"""
        # 取消之前的定时器
        if self.scroll_timer:
            self.scroll_timer.stop()

        # 保存待加载的股票
        self.pending_load_key = key

        # 创建新的定时器，300ms后加载
        self.scroll_timer = QTimer()
        self.scroll_timer.setSingleShot(True)
        self.scroll_timer.timeout.connect(self.execute_delayed_load)
        self.scroll_timer.start(300)  # 300ms延迟


        # 更新状态栏显示
        self.statusBar().showMessage(f"准备加载: {title}...")

    def execute_delayed_load(self):
        """执行延迟加载"""
        if self.pending_load_key and not self.is_loading:
            self.statusBar().showMessage(f"正在加载图表...")
            self.is_loading = True

            try:
                self.load_stock_data(self.pending_load_key)
                # 获取股票标题用于状态显示
                if self.pending_load_key in self.data_dict:
                    _, title = self.data_dict[self.pending_load_key]
                    self.statusBar().showMessage(f"已加载: {title}")
            except Exception as e:
                self.statusBar().showMessage(f"加载失败: {e}")
            finally:
                self.is_loading = False
                self.pending_load_key = None

    # 删除保存图片功能

    def calculate_change_percentage(self, df, current_timestamp, current_price, price_type='close'):
        """计算涨幅 - 基于前一日15:00的价格"""
        try:
            pass
            if df is None or len(df) == 0:
                return None, None

            # 获取当前数据在DataFrame中的位置
            current_index = None
            for i, ts in enumerate(df.index):
                pass
                if ts == current_timestamp:
                    current_index = i
                    break

            if current_index is None:
                return None, None

            # 所有时间点都统一使用上一个交易日收盘价作为基准
            # 由于LT.h5中存储的都是交易日数据，直接寻找上一个交易日的15:00数据
            target_index = None
            current_date = current_timestamp.date()

            # 从当前位置往前查找上一个交易日的15:00数据
            for i in range(current_index - 1, -1, -1):
                check_ts = df.index[i]
                check_date = check_ts.date()

                # 找到不同交易日的15:00数据点
                if (check_date < current_date and
                    check_ts.hour == 15 and check_ts.minute == 0):
                    target_index = i
                    break

            if target_index is not None:
                # 使用上一个交易日15:00的收盘价作为基准
                base_price = df.iloc[target_index]['close']  # 明确使用收盘价
                change = current_price - base_price
                change_pct = (change / base_price) * 100 if base_price != 0 else 0
                return change, change_pct
            else:
                # 如果找不到上一个交易日的15:00数据，尝试找上一个交易日的最后一个收盘价
                for i in range(current_index - 1, -1, -1):
                    check_ts = df.index[i]
                    check_date = check_ts.date()
                    if check_date < current_date:
                        base_price = df.iloc[i]['close']  # 使用收盘价
                        change = current_price - base_price
                        change_pct = (change / base_price) * 100 if base_price != 0 else 0
                        return change, change_pct
                return None, None

        except Exception as e:
            return None, None

    def update_info_display(self, timestamp, row_data):
        """更新信息显示 - 保持滚动位置"""
        try:
            # 保存当前滚动位置
            scrollbar = self.info_display.verticalScrollBar()
            current_scroll_position = scrollbar.value()
            info_text = f"📅 时间: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
            info_text += "=" * 40 + "\n"

            # OHLC数据
            info_text += "📊 OHLC数据:\n"
            info_text += f"  开盘: {row_data.get('open', 'N/A'):.2f}\n"
            info_text += f"  最高: {row_data.get('high', 'N/A'):.2f}\n"
            info_text += f"  最低: {row_data.get('low', 'N/A'):.2f}\n"
            info_text += f"  收盘: {row_data.get('close', 'N/A'):.2f}\n"

            # 收盘价涨跌幅 - 使用前一日15:00收盘价作为基准
            if 'close' in row_data and self.current_key and self.current_key in self.data_dict:
                df, _ = self.data_dict[self.current_key]
                close_change, close_change_pct = self.calculate_change_percentage(df, timestamp, row_data['close'], 'close')
                if close_change is not None and close_change_pct is not None:
                    color_emoji = "🔴" if close_change > 0 else "🟢" if close_change < 0 else "⚪"
                    info_text += f"  收盘价涨跌: {close_change:+.2f} ({close_change_pct:+.2f}%) {color_emoji}\n"

            # 最高价涨跌幅 - 使用前一日15:00收盘价作为基准
            if 'high' in row_data and self.current_key and self.current_key in self.data_dict:
                df, _ = self.data_dict[self.current_key]
                high_change, high_change_pct = self.calculate_change_percentage(df, timestamp, row_data['high'], 'close')
                if high_change is not None and high_change_pct is not None:
                    color_emoji = "🔴" if high_change > 0 else "🟢" if high_change < 0 else "⚪"
                    info_text += f"  最高价涨跌: {high_change:+.2f} ({high_change_pct:+.2f}%) {color_emoji}\n"

            # 成交量
            if 'volume' in row_data and not pd.isna(row_data['volume']):
                volume = row_data['volume']
                if volume >= 100000000:
                    info_text += f"  成交量: {volume/100000000:.2f}亿\n"
                elif volume >= 10000:
                    info_text += f"  成交量: {volume/10000:.2f}万\n"
                else:
                    info_text += f"  成交量: {volume:,.0f}\n"

            info_text += "\n"

            # 均线数据
            ma_data = []
            for ma_col, config in ma_config.items():
                pass
                if ma_col in row_data and not pd.isna(row_data[ma_col]):
                    ma_data.append((config['name'], row_data[ma_col]))

            if ma_data:
                info_text += "📈 均线数据:\n"
                for ma_name, ma_value in ma_data:
                    info_text += f"  {ma_name}: {ma_value:.2f}\n"
                info_text += "\n"

            # 计算实体比例技术指标
            info_text += "🔧 技术指标:\n"

            try:
                open_price = float(row_data['open'])
                high_price = float(row_data['high'])
                low_price = float(row_data['low'])
                close_price = float(row_data['close'])

                # 上影整体比例 (high-max(open,close))/(high-low)
                if high_price != low_price:
                    upper_shadow_ratio = (high_price - max(open_price, close_price)) / (high_price - low_price)
                    info_text += f"  上影整体比例: {upper_shadow_ratio:.4f}\n"

                # 上影实体比例 (high-max(open,close))/abs(open-close)
                if abs(open_price - close_price) > 0:
                    upper_shadow_body_ratio = (high_price - max(open_price, close_price)) / abs(open_price - close_price)
                    info_text += f"  上影实体比例: {upper_shadow_body_ratio:.4f}\n"

                # 下影整体比例 (min(open,close)-low)/(high-low)
                if high_price != low_price:
                    lower_shadow_ratio = (min(open_price, close_price) - low_price) / (high_price - low_price)
                    info_text += f"  下影整体比例: {lower_shadow_ratio:.4f}\n"

                # 下影实体比例 (min(open,close)-low)/abs(open-close)
                if abs(open_price - close_price) > 0:
                    lower_shadow_body_ratio = (min(open_price, close_price) - low_price) / abs(open_price - close_price)
                    info_text += f"  下影实体比例: {lower_shadow_body_ratio:.4f}\n"

                # 实体整体比例 abs(open-close)/(high-low)
                if high_price != low_price:
                    body_ratio = abs(open_price - close_price) / (high_price - low_price)
                    info_text += f"  实体整体比例: {body_ratio:.4f}\n"

            except (ValueError, KeyError, ZeroDivisionError) as e:
                info_text += f"  技术指标计算错误: {str(e)}\n"

            self.info_display.setText(info_text)

            # 恢复滚动位置
            scrollbar.setValue(current_scroll_position)

        except Exception as e:
            self.info_display.setText(f"显示数据时出错: {str(e)}")

    def move_items_up(self):
        """上移选中的项目"""
        selected_items = self.stock_list.selectedItems()
        if not selected_items:
            return

        # 按行号排序，从上到下处理
        items_with_rows = [(self.stock_list.row(item), item) for item in selected_items]
        items_with_rows.sort(key=lambda x: x[0])

        # 检查是否可以上移（最上面的项目不能再上移）
        if items_with_rows[0][0] == 0:
            return

        # 执行上移
        for row, item in items_with_rows:
            if row > 0:
                # 取出项目
                taken_item = self.stock_list.takeItem(row)
                # 插入到上一行
                self.stock_list.insertItem(row - 1, taken_item)
                # 保持选中状态
                taken_item.setSelected(True)

    def move_items_to_top(self):
        """上移选中项目到顶部"""
        selected_items = self.stock_list.selectedItems()
        if not selected_items:
            return

        # 按行号排序，从下到上处理
        items_with_rows = [(self.stock_list.row(item), item) for item in selected_items]
        items_with_rows.sort(key=lambda x: x[0], reverse=True)

        # 执行移动到顶部
        for i, (row, item) in enumerate(items_with_rows):
            # 取出项目
            taken_item = self.stock_list.takeItem(row)
            # 插入到顶部
            self.stock_list.insertItem(len(items_with_rows) - 1 - i, taken_item)
            # 保持选中状态
            taken_item.setSelected(True)

    def move_items_down(self):
        """下移选中的项目"""
        selected_items = self.stock_list.selectedItems()
        if not selected_items:
            return

        # 按行号排序，从下到上处理
        items_with_rows = [(self.stock_list.row(item), item) for item in selected_items]
        items_with_rows.sort(key=lambda x: x[0], reverse=True)

        # 检查是否可以下移（最下面的项目不能再下移）
        if items_with_rows[0][0] == self.stock_list.count() - 1:
            return

        # 执行下移
        for row, item in items_with_rows:
            if row < self.stock_list.count() - 1:
                # 取出项目
                taken_item = self.stock_list.takeItem(row)
                # 插入到下一行
                self.stock_list.insertItem(row + 1, taken_item)
                # 保持选中状态
                taken_item.setSelected(True)

    def move_items_to_bottom(self):
        """下移选中项目到底部"""
        selected_items = self.stock_list.selectedItems()
        if not selected_items:
            return

        # 按行号排序，从上到下处理
        items_with_rows = [(self.stock_list.row(item), item) for item in selected_items]
        items_with_rows.sort(key=lambda x: x[0])

        # 执行移动到底部
        for i, (row, item) in enumerate(items_with_rows):
            # 取出项目
            taken_item = self.stock_list.takeItem(row - i)  # 减去i是因为前面的项目已经被移除
            # 添加到底部
            self.stock_list.addItem(taken_item)
            # 保持选中状态
            taken_item.setSelected(True)

    def distribute_items(self):
        """分布排列：15分钟数据自动调整到同名同日期的60分钟数据下面"""
        selected_items = self.stock_list.selectedItems()
        if not selected_items:
            return

        # 收集所有项目信息
        all_items = []
        for i in range(self.stock_list.count()):
            item = self.stock_list.item(i)
            all_items.append({
                'item': item,
                'text': item.text(),
                'key': item.data(Qt.ItemDataRole.UserRole),
                'selected': item.isSelected()
            })

        # 调试：打印所有项目文本，帮助了解数据格式
        print("=== 分布排列调试信息 ===")
        print(f"总共 {len(all_items)} 个项目:")
        for i, item_info in enumerate(all_items):
            print(f"{i+1}. {item_info['text']}")

        # 分析15分钟和60分钟数据的对应关系
        minute_15_items = []
        minute_60_items = []
        other_items = []

        for item_info in all_items:
            text = item_info['text']
            if '15分钟' in text:
                minute_15_items.append(item_info)
                print(f"识别为15分钟数据: {text}")
            elif '60分钟' in text:
                minute_60_items.append(item_info)
                print(f"识别为60分钟数据: {text}")
            else:
                other_items.append(item_info)
                print(f"识别为其他数据: {text}")

        print(f"\n分类结果:")
        print(f"15分钟数据: {len(minute_15_items)} 个")
        print(f"60分钟数据: {len(minute_60_items)} 个")
        print(f"其他数据: {len(other_items)} 个")

        # 重新排列：先放60分钟数据，然后在每个60分钟数据后面放对应的15分钟数据
        new_order = []

        # 添加其他数据（非15分钟和60分钟的数据）
        new_order.extend(other_items)

        # 处理60分钟数据和对应的15分钟数据
        for minute_60_info in minute_60_items:
            new_order.append(minute_60_info)

            # 查找对应的15分钟数据
            minute_60_text = minute_60_info['text']
            # 提取股票名称和日期信息用于匹配
            stock_name_60, date_60 = self.extract_stock_and_date(minute_60_text)

            # 找到匹配的15分钟数据
            matching_15m = []
            for minute_15_info in minute_15_items:
                stock_name_15, date_15 = self.extract_stock_and_date(minute_15_info['text'])
                if stock_name_60 == stock_name_15 and date_60 == date_15:
                    matching_15m.append(minute_15_info)

            # 按时间排序15分钟数据
            matching_15m.sort(key=lambda x: self.extract_time_from_text(x['text']))
            new_order.extend(matching_15m)

        # 添加没有匹配的15分钟数据
        matched_15m_items = set()
        for minute_60_info in minute_60_items:
            matching_items = self.find_matching_15m(minute_60_info, minute_15_items)
            for item in matching_items:
                matched_15m_items.add(id(item))

        unmatched_15m = []
        for minute_15_info in minute_15_items:
            if id(minute_15_info) not in matched_15m_items:
                unmatched_15m.append(minute_15_info)
        new_order.extend(unmatched_15m)

        # 重新构建列表
        self.rebuild_stock_list(new_order)

    def gather_items_to_bottom(self):
        """底部汇集：15分钟数据汇集到底部"""
        selected_items = self.stock_list.selectedItems()
        if not selected_items:
            return

        # 收集所有项目信息
        all_items = []
        for i in range(self.stock_list.count()):
            item = self.stock_list.item(i)
            all_items.append({
                'item': item,
                'text': item.text(),
                'key': item.data(Qt.ItemDataRole.UserRole),
                'selected': item.isSelected()
            })

        # 分离15分钟数据和其他数据
        minute_15_items = []
        other_items = []

        for item_info in all_items:
            if '15分钟' in item_info['text']:
                minute_15_items.append(item_info)
            else:
                other_items.append(item_info)

        # 重新排列：其他数据在前，15分钟数据在后
        new_order = other_items + minute_15_items

        # 重新构建列表
        self.rebuild_stock_list(new_order)

    def extract_stock_and_date(self, text):
        """从文本中提取股票名称和日期信息"""
        try:
            print(f"提取股票和日期信息: {text}")

            # 处理15分钟数据格式
            if '15分钟' in text:
                # 尝试多种可能的分隔符
                separators = [' 15分钟_', ' 15分钟 ', '_15分钟_', '_15分钟 ']
                for separator in separators:
                    if separator in text:
                        parts = text.split(separator)
                        if len(parts) == 2:
                            stock_name = parts[0].strip()
                            datetime_str = parts[1].strip()
                            # 提取日期部分
                            date_part = datetime_str.split(' ')[0] if ' ' in datetime_str else datetime_str.split('_')[0]
                            print(f"  15分钟数据({separator}) - 股票: {stock_name}, 日期: {date_part}")
                            return stock_name, date_part

                # 如果没有找到分隔符，尝试简单的空格分割
                if ' ' in text:
                    parts = text.split(' ')
                    if len(parts) >= 3:  # 至少包含股票名、15分钟、日期
                        stock_name = parts[0].strip()
                        # 找到日期部分（通常包含-或数字）
                        for part in parts[2:]:
                            if '-' in part or part.replace(':', '').replace('-', '').isdigit():
                                date_part = part.split(' ')[0] if ' ' in part else part
                                print(f"  15分钟数据(空格分割) - 股票: {stock_name}, 日期: {date_part}")
                                return stock_name, date_part

            # 处理60分钟数据格式
            elif '60分钟' in text:
                # 尝试多种可能的分隔符
                separators = [' 60分钟_', ' 60分钟 ', '_60分钟_', '_60分钟 ']
                for separator in separators:
                    if separator in text:
                        parts = text.split(separator)
                        if len(parts) == 2:
                            stock_name = parts[0].strip()
                            datetime_str = parts[1].strip()
                            # 提取日期部分
                            date_part = datetime_str.split(' ')[0] if ' ' in datetime_str else datetime_str.split('_')[0]
                            print(f"  60分钟数据({separator}) - 股票: {stock_name}, 日期: {date_part}")
                            return stock_name, date_part

                # 如果没有找到分隔符，尝试简单的空格分割
                if ' ' in text:
                    parts = text.split(' ')
                    if len(parts) >= 3:  # 至少包含股票名、60分钟、日期
                        stock_name = parts[0].strip()
                        # 找到日期部分（通常包含-或数字）
                        for part in parts[2:]:
                            if '-' in part or part.replace(':', '').replace('-', '').isdigit():
                                date_part = part.split(' ')[0] if ' ' in part else part
                                print(f"  60分钟数据(空格分割) - 股票: {stock_name}, 日期: {date_part}")
                                return stock_name, date_part

            print(f"  无法识别格式，返回原文本: {text}")
            return text, ""
        except Exception as e:
            print(f"  提取失败: {e}")
            return text, ""

    def extract_time_from_text(self, text):
        """从文本中提取时间信息用于排序"""
        try:
            if '15分钟_' in text:
                parts = text.split(' 15分钟_')
                if len(parts) == 2:
                    datetime_str = parts[1].strip()
                    return datetime_str
            return text
        except:
            return text

    def find_matching_15m(self, minute_60_info, minute_15_items):
        """查找与60分钟数据匹配的15分钟数据"""
        stock_name_60, date_60 = self.extract_stock_and_date(minute_60_info['text'])
        matching = []
        for minute_15_info in minute_15_items:
            stock_name_15, date_15 = self.extract_stock_and_date(minute_15_info['text'])
            if stock_name_60 == stock_name_15 and date_60 == date_15:
                matching.append(minute_15_info)
        return matching

    def rebuild_stock_list(self, new_order):
        """根据新顺序重建股票列表"""
        print(f"\n=== 重建股票列表 ===")
        print(f"新顺序包含 {len(new_order)} 个项目:")
        for i, item_info in enumerate(new_order):
            print(f"{i+1}. {item_info['text']}")

        # 临时断开信号连接
        try:
            self.stock_list.currentItemChanged.disconnect()
        except:
            pass  # 如果没有连接的信号，忽略错误

        # 记录当前选中的项目
        current_key = None
        current_item = self.stock_list.currentItem()
        if current_item:
            current_key = current_item.data(Qt.ItemDataRole.UserRole)

        # 清空列表
        self.stock_list.clear()

        # 按新顺序添加项目
        for item_info in new_order:
            new_item = QListWidgetItem(item_info['text'])
            new_item.setData(Qt.ItemDataRole.UserRole, item_info['key'])

            # 保持选中状态
            if item_info['selected']:
                new_item.setSelected(True)

            # 保持15分钟数据的橙黄色
            if '15分钟' in item_info['text']:
                from PyQt6.QtGui import QColor
                new_item.setForeground(QColor(255, 165, 0))

            self.stock_list.addItem(new_item)

        # 恢复当前选中项目
        if current_key:
            for i in range(self.stock_list.count()):
                item = self.stock_list.item(i)
                if item.data(Qt.ItemDataRole.UserRole) == current_key:
                    self.stock_list.setCurrentItem(item)
                    break

        # 重新连接信号
        self.stock_list.currentItemChanged.connect(self.on_stock_list_changed)

    def delete_selected_items(self):
        """删除选中的项目"""
        selected_items = self.stock_list.selectedItems()
        if not selected_items:
            return

        # 确认删除
        from PyQt6.QtWidgets import QMessageBox
        reply = QMessageBox.question(self, "确认删除",
                                   f"确定要删除选中的 {len(selected_items)} 个项目吗？",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                   QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            # 按行号排序，从下到上删除（避免索引变化问题）
            items_with_rows = [(self.stock_list.row(item), item) for item in selected_items]
            items_with_rows.sort(key=lambda x: x[0], reverse=True)

            # 删除对应的数据
            for row, item in items_with_rows:
                key = item.data(Qt.ItemDataRole.UserRole)
                if key and key in self.data_dict:
                    del self.data_dict[key]
                # 删除列表项
                self.stock_list.takeItem(row)

    def clear_stock_list(self):
        """清空股票列表"""
        if self.stock_list.count() == 0:
            return

        # 确认清空
        from PyQt6.QtWidgets import QMessageBox
        reply = QMessageBox.question(self, "确认清空",
                                   "确定要清空所有股票列表吗？",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                   QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            # 清空数据字典
            self.data_dict.clear()
            # 清空列表
            self.stock_list.clear()
            # 清空图表
            self.chart_widget.figure.clear()
            self.chart_widget.draw()
            # 重置当前key
            self.current_key = None

    def export_selected_items(self):
        """导出选中的项目到HDF文件"""
        selected_items = self.stock_list.selectedItems()
        if not selected_items:
            return

        try:
            # 选择保存文件路径
            from PyQt6.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出到HDF文件",
                "exported_stocks.h5",
                "HDF5 Files (*.h5);;All Files (*)"
            )

            if not file_path:
                return  # 用户取消了保存

            # 收集要导出的数据
            export_data = {}
            export_count = 0

            for item in selected_items:
                key = item.data(Qt.ItemDataRole.UserRole)
                if key and key in self.data_dict:
                    df, title = self.data_dict[key]

                    # 处理数据：清理列名和转换MA
                    df_export = df.copy()

                    # 清理第一列的列标题，删除"5分钟线"、"前复权"等
                    if len(df_export.columns) > 0:
                        first_col = df_export.columns[0]
                        if isinstance(first_col, str):
                            cleaned_first_col = first_col.replace("5分钟线", "").replace("前复权", "").replace("分钟线", "").strip()
                            # 重命名第一列
                            df_export = df_export.rename(columns={first_col: cleaned_first_col})

                    # 将所有MA列名转换为小写ma
                    column_mapping = {}
                    for col in df_export.columns:
                        if isinstance(col, str):
                            # 检查是否是MA列（MA5, MA10, MA20等）
                            if col.upper().startswith('MA') and col[2:].isdigit():
                                new_col = col.lower()  # MA5 -> ma5
                                column_mapping[col] = new_col

                    # 应用列名映射
                    if column_mapping:
                        df_export = df_export.rename(columns=column_mapping)

                    # 生成节点名称
                    # 如果是15分钟数据，保持原有格式
                    if '15分钟' in item.text():
                        # 从标题中提取股票代码和时间信息
                        item_text = item.text()
                        try:
                            # 解析格式：股票名称 15分钟_YYYY-MM-DD HH:MM
                            parts = item_text.split(' 15分钟_')
                            if len(parts) == 2:
                                stock_name = parts[0].strip()
                                datetime_str = parts[1].strip()

                                # 尝试从股票名称中提取代码（假设格式为：名称(代码)）
                                if '(' in stock_name and ')' in stock_name:
                                    stock_code = stock_name.split('(')[1].split(')')[0]
                                else:
                                    # 如果没有括号，使用序号
                                    stock_code = f"{export_count:06d}"

                                # 转换时间格式：2024-09-12 15:00 -> 2024_09_12 1500
                                # 先处理日期部分（替换-为_），然后处理时间部分（移除:），最后用空格连接
                                date_part, time_part = datetime_str.split(' ')
                                date_formatted = date_part.replace('-', '_')  # 2024-09-12 -> 2024_09_12
                                time_formatted = time_part.replace(':', '')   # 15:00 -> 1500
                                datetime_formatted = f"{date_formatted} {time_formatted}"  # 2024_09_12 1500
                                node_name = f"/stock_{stock_code}_15m_{datetime_formatted}"
                            else:
                                node_name = f"/exported_15m_{export_count:03d}"
                        except:
                            node_name = f"/exported_15m_{export_count:03d}"
                    else:
                        # 原始LT数据，保持原有格式
                        try:
                            # 尝试从key中提取信息
                            if key.startswith('/stock_') and '_60m_' in key:
                                node_name = key  # 保持原有节点名称
                            else:
                                # 生成新的节点名称
                                node_name = f"/exported_stock_{export_count:03d}"
                        except:
                            node_name = f"/exported_stock_{export_count:03d}"

                    export_data[node_name] = df_export
                    export_count += 1

            if not export_data:
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "警告", "没有找到可导出的数据")
                return

            # 写入HDF文件
            with pd.HDFStore(file_path, 'w') as store:
                for node_name, df in export_data.items():
                    store[node_name] = df

            # 显示成功消息
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "导出成功",
                f"成功导出 {len(export_data)} 个条目到文件：\n{file_path}\n\n节点列表：\n" +
                "\n".join(export_data.keys())
            )

        except Exception as e:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "导出失败", f"导出过程中发生错误：\n{str(e)}")


def clear_console():
    """清空控制台"""
    import os
    os.system('cls' if os.name == 'nt' else 'clear')

def main():
    """主函数"""
    # 清空控制台
    clear_console()

    # PyQt6兼容性处理
    try:
        QApplication.setHighDpiScaleFactorRoundingPolicy(
            Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    except:
        pass

    try:
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    except:
        pass

    try:
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    except:
        pass

    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # 设置应用信息
    app.setApplicationName("股票看盘软件")
    app.setApplicationVersion("完美K线版-1.0")
    app.setOrganizationName("Stock Analysis Tools")

    # 创建并显示主窗口
    window = StockViewerWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
